import React, { Fragment, useEffect, useState } from "react";
import Layout from "../../layout/default";
import {
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Box,
  Flex,
  Button,
  Text,
  Input,
  Modal,
  FormControl,
  FormLabel,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  Td,
  TableContainer,
  useDisclosure,
  Checkbox,
  InputGroup,
  InputRightElement,
  useToast,
  Badge,
  Spinner,
  Tooltip,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
} from "@chakra-ui/react";
import axios from "axios";
import { setToast } from "../../functions/Toastfunction";
import ConfirmationModal from "../../functions/Modals/ConfimationModals";
import { Link, useNavigate } from "react-router-dom";
import {  MdDelete, MdEdit } from "react-icons/md";
import { IoMdArrowRoundBack } from "react-icons/io";
import { useSelector } from "react-redux";

const AdminUsers = () => {
  const [userid, setUserid] = useState(null);
  const [confirmisOpen, setConfirmisOpen] = useState(false);
  const openConfirmModal = (id) => {
    setConfirmisOpen(true);
    setUserid(id);
  };
  const closeConfirmModal = () => setConfirmisOpen(false);
  const [showupdatepassword, setShowUpdatepassword] = useState(false);
  const [updatebutton, setUpdateButton] = useState(false);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();
  const navigate = useNavigate();
  const [pass, setPass] = useState(false);
  const handlepassClick = () => setPass(!pass);
  const [confirmpass, setConfirmpass] = useState(false);
  const handleconfirmpassClick = () => setConfirmpass(!confirmpass);
  const [roles, setRoles] = useState([]);
  const [user, setUser] = useState(null);
  const [render, setRender] = useState(false);
  const [toggle, setToggle] = useState(false);
  const [query, setQuery] = useState("");
  const [selectedRoles, setSelectedRoles] = useState([]);
  const [showSelectedRoles, setShowSelectedRoles] = useState([]);
  const [userDetail, setUserDetail] = useState({
    name: "",
    username: "",
    password: "",
    confirm_password: "",
  });
  const userData = useSelector((state) => state.user);
  // console.log(user,"user")
  const addRolesHandler = (e) => {
    e.stopPropagation();
    setToggle(true);
  };

  function handlesaveSelectedRoles() {
    setToggle(false);
    setQuery("");
    setShowSelectedRoles(selectedRoles);
  }

  function validateEmail(email) {
    var re = /\S+@\S+\.\S+/;
    return re.test(email);
  }

  const [updateloader, setUpdateLoader] = useState(false);
  function handleUpdateUserHitApi() {
    if (showupdatepassword) {
      if (userDetail.password === "" || userDetail.confirm_password === "") {
        setToast(toast, "Please Enter Password", "", "error");
        return;
      }
      if (userDetail.password !== userDetail.confirm_password) {
        setToast(toast, "Password Does not Match", "", "error");
        return;
      }
    } else {
      delete userDetail.password;
      delete userDetail.confirm_password;
    }

    // if (Object.values(userDetail).includes("")) {
    //     setToast(toast, "Please fill All the details", "", "error");
    //     return;
    // }

    if (showupdatepassword && userDetail.confirm_password === undefined) {
      setToast(toast, "Please Enter Password", "", "error");
      return;
    }
    if(!validateEmail(userDetail.username)){
      setToast(toast, "Please Enter Valid Email", "", "error");
      return;
    }

    delete userDetail._id;
    setUpdateLoader(true);
    const headers = {
      Authorization: sessionStorage.getItem("admintoken"),
    };

    axios({
      method: "PUT",
      url: `${process.env.REACT_APP_BASE_URL}/api/user/${userid}`,
      data: { ...userDetail, userGroup: selectedRoles },
      headers,
    })
      .then((r) => {
        setToast(toast, `User Updated Successfully `, "", "success");
        onClose();
        setRender(!render);
        setUpdateLoader(false);
      })
      .catch((err) => {
        if (err.response.data.err === "Invalid token") {
          sessionStorage.removeItem("admintoken");
          navigate("/login");
          return;
        }
        setUpdateLoader(false);
        if (err.response.status === 403) {
          setToast(
            toast,
            `You don't have an access to perform this action`,
            "",
            "warning"
          );
        } else {
          setToast(toast, `${err.response.data.err}`, "", "error");
        }
      });
  }

  function handleEditUserButtonClicked(user) {
    setUserid(user._id);
    const merged = Object.assign(userDetail, user);

    setUserDetail(merged);
    setSelectedRoles(merged.userGroup);
    setShowSelectedRoles(merged.userGroup);
    setUpdateButton(true);
    onOpen();
  }

  function handleInputChange(e) {
    const { name, value } = e.target;

    // Check if the first character is a space
    if (value.charAt(0) === " ") {
      // Remove the first character (space)
      e.target.value = value.slice(1);
      return;
    }
    if (name === "confirm_password") {
      if (e.nativeEvent?.inputType === "deleteContentBackward") {
        setUserDetail({
          ...userDetail,
          [name]: value,
        });
        return;
      }
      if (userDetail.password === "") {
        setToast(toast, "Enter your password First", "", "error");
        return;
      }
    }

    setUserDetail({
      ...userDetail,
      [name]: value,
    });
  }

  const [createloader, setCreateLoader] = useState(false);
  const handleCreateUserHitApi = () => {
    if (createloader) {
      return;
    }
    if (userDetail.password !== userDetail.confirm_password) {
      setToast(toast, "Password Does not Match", "", "error");
      return;
    }
    // if (Object.values(userDetail).includes("")) {
    //     setToast(toast, "Please fill All the details", "", "error");
    //     return;
    // }

    if(!validateEmail(userDetail.username)){
      setToast(toast, "Please Enter Valid Email", "", "error");
      return;
    }

    setCreateLoader(true);
    const headers = {
      Authorization: sessionStorage.getItem("admintoken"),
    };
    axios({
      method: "POST",
      url: `${process.env.REACT_APP_BASE_URL}/api/user`,
      data: { ...userDetail, userGroup: selectedRoles },
      headers,
    })
      .then((r) => {
        setCreateLoader(false);
        setToast(toast, `User Successfully Created`, "", "success");
        onClose();
        setRender(!render);
      })
      .catch((err) => {
        if (err.response.data.err === "Invalid token") {
          sessionStorage.removeItem("admintoken");
          navigate("/login");
          return;
        }
        setCreateLoader(false);
        if (err.response.status === 403) {
          setToast(
            toast,
            `You don't have an access to perform this action`,
            "",
            "warning"
          );
        } else {
          setToast(toast, `${err.response.data.err}`, "", "error");
        }
      });
  };

  function getUserRolesfromBackend() {
    const headers = {
      Authorization: sessionStorage.getItem("admintoken"),
    };
    axios({
      method: "GET",
      url: `${process.env.REACT_APP_BASE_URL}/api/userGroup`,
      headers,
    })
      .then((r) => {
        setRoles(r.data);
      })
      .catch((err) => {
        if (err.response.data.err === "Invalid token") {
          sessionStorage.removeItem("admintoken");
          navigate("/login");
          return;
        }
        if (err.response.status === 403) {
          setToast(
            toast,
            `You don't have an access to perform this action`,
            "",
            "warning"
          );
        } else {
          setToast(toast, `${err.response.data.err}`, "", "error");
        }
      });
  }
  let [userLoading, setUserLoading] = useState(false);
  function getAllUsersFromBackend() {
    if (userLoading) {
      return;
    }
    setUserLoading(true);
    const headers = {
      Authorization: sessionStorage.getItem("admintoken"),
    };
    axios({
      method: "GET",
      url: `${process.env.REACT_APP_BASE_URL}/api/user/all`,
      headers,
    })
      .then((r) => {
        setUser(r.data);
        setUserLoading(false);
      })
      .catch((err) => {
        if (err.response.data.err === "Invalid token") {
          sessionStorage.removeItem("admintoken");
          navigate("/login");
          return;
        }
        // console.log(err);
        if (err.response.status === 403) {
          setToast(
            toast,
            `You don't have an access to perform this action`,
            "",
            "warning"
          );
        } else {
          setToast(toast, `${err.response.data.err}`, "", "error");
        }
        setUserLoading(false);
      });
  }
  const [deleteloading, setDeleteLoading] = useState(false);
  function handleDeleteUserButtonClickedApiHit() {
    if (deleteloading) {
      return;
    }
    setDeleteLoading(true);
    const headers = {
      Authorization: sessionStorage.getItem("admintoken"),
    };
    axios({
      method: "DELETE",
      url: `${process.env.REACT_APP_BASE_URL}/api/user/${userid}`,
      headers,
    })
      .then((r) => {
        setDeleteLoading(false);
        setToast(toast, `${r.data.success}`, "", "success");
        setRender(!render);
        closeConfirmModal();
      })
      .catch((err) => {
        if (err.response.data.err === "Invalid token") {
          sessionStorage.removeItem("admintoken");
          navigate("/login");
          return;
        }
        setDeleteLoading(false);
        if (err.response.status === 403) {
          setToast(
            toast,
            `You don't have an access to perform this action`,
            "",
            "warning"
          );
        } else {
          setToast(toast, `${err.response.data.err}`, "", "error");
        }
      });
  }

  useEffect(() => {
    getUserRolesfromBackend();
    getAllUsersFromBackend();
  }, [render]);

  return (
    <Box bgColor={"#f2f2f2"}>
      <Layout title="User | Administration" content="container">
        <Flex justifyContent={"space-between"} alignItems={"center"} mb={6}>
          <Flex justifyContent={"center"} alignItems={"center"}>
            <Tooltip label="Back">
              <Text
                mr={3}
                as={"span"}
                fontSize={"28px"}
                cursor={"pointer"}
                onClick={() => navigate(-1)}
              >
                <IoMdArrowRoundBack />
              </Text>
            </Tooltip>
            <Breadcrumb fontWeight="medium" fontSize="sm">
              <BreadcrumbItem>
                <Link to={"/"}>Dashboard</Link>
              </BreadcrumbItem>

              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">User</BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>
          </Flex>
          {userLoading ||
          !userData?.accessScopes?.user?.includes("write") ? null : (
            <Flex justifyContent="space-between">
              <Flex gap="10px">
                <Button
                  variant={"outline"}
                  colorScheme="teal"
                  size={"sm"}
                  py={5}
                  px={4}
                  onClick={() => {
                    setUpdateButton(false);
                    onOpen();
                    setUserDetail({
                      name: "",
                      username: "",
                      password: "",
                      confirm_password: "",
                      phone: "",
                    });
                    setSelectedRoles([]);
                    setShowSelectedRoles([]);
                  }}
                >
                  Create Users
                </Button>
              </Flex>
            </Flex>
          )}
        </Flex>
        {userLoading ? (
          <Flex justifyContent={"center"} alignItems={"center"} mt={16}>
            <Spinner size={"lg"} />
          </Flex>
        ) : (
          <Box width="100%">
            <Modal
              closeOnOverlayClick={false}
              isOpen={isOpen}
              onClose={onClose}
              p={3}
            >
              <ModalOverlay />
              <ModalContent>
                <ModalHeader>
                  {updatebutton ? "Update User" : "Create New User"}
                </ModalHeader>
                <ModalCloseButton />
                <ModalBody p={6} textAlign="left">
                  <FormControl mb="10px">
                    <FormLabel>Name</FormLabel>
                    <Input
                      onChange={handleInputChange}
                      name="name"
                      value={userDetail.name}
                      placeholder="Enter user name"
                    />
                  </FormControl>
                  <FormControl mb="10px">
                    <FormLabel>Email</FormLabel>
                    <Input
                      value={userDetail.username}
                      onChange={handleInputChange}
                      name="username"
                      type="email"
                      placeholder="Enter user id (email)"
                    />
                  </FormControl>

                  {/* <FormControl mb="10px">
              <FormLabel>Phone Number</FormLabel>
              <Input
                type="number"
                onChange={handleInputChange}
                name="phone"
                value={userDetail.phone}
              />
            </FormControl> */}
                  {/* ************************** FOR UPDATING ************************************** */}
                  {updatebutton ? (
                    <Box mb={3}>
                      <Checkbox
                        size="lg"
                        colorScheme="orange"
                        fontWeight="semibold"
                        mb={2}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setShowUpdatepassword(true);
                          } else {
                            setShowUpdatepassword(false);
                          }
                        }}
                      >
                        Update Password
                      </Checkbox>
                      {showupdatepassword ? (
                        <>
                          <FormControl mb="10px">
                            <FormLabel>Update Password</FormLabel>
                            <InputGroup size="md">
                              <Input
                                name="password"
                                onChange={handleInputChange}
                                pr="4.5rem"
                                type={pass ? "text" : "password"}
                                placeholder="Enter password"
                              />
                              <InputRightElement width="4.5rem">
                                <Button
                                  h="1.75rem"
                                  size="sm"
                                  onClick={handlepassClick}
                                >
                                  {pass ? "Hide" : "Show"}
                                </Button>
                              </InputRightElement>
                            </InputGroup>
                          </FormControl>
                          <FormControl mb="10px">
                            <FormLabel>Confirm Password</FormLabel>
                            <InputGroup size="md">
                              <Input
                                name="confirm_password"
                                onChange={handleInputChange}
                                pr="4.5rem"
                                type={confirmpass ? "text" : "password"}
                                placeholder="Confirm password"
                              />
                              <InputRightElement width="4.5rem">
                                <Button
                                  h="1.75rem"
                                  size="sm"
                                  onClick={handleconfirmpassClick}
                                >
                                  {confirmpass ? "Hide" : "Show"}
                                </Button>
                              </InputRightElement>
                            </InputGroup>
                          </FormControl>
                        </>
                      ) : null}
                    </Box>
                  ) : (
                    <Box>
                      <FormControl mb="10px">
                        <FormLabel>Enter Password</FormLabel>
                        <InputGroup size="md">
                          <Input
                            name="password"
                            onChange={handleInputChange}
                            pr="4.5rem"
                            type={pass ? "text" : "password"}
                            placeholder="Enter password"
                          />
                          <InputRightElement width="4.5rem">
                            <Button
                              h="1.75rem"
                              size="sm"
                              onClick={handlepassClick}
                            >
                              {pass ? "Hide" : "Show"}
                            </Button>
                          </InputRightElement>
                        </InputGroup>
                      </FormControl>
                      <FormControl mb="10px">
                        <FormLabel>Confirm Password</FormLabel>
                        <InputGroup size="md">
                          <Input
                            name="confirm_password"
                            onChange={handleInputChange}
                            pr="4.5rem"
                            type={confirmpass ? "text" : "password"}
                            placeholder="Confirm password"
                          />
                          <InputRightElement width="4.5rem">
                            <Button
                              h="1.75rem"
                              size="sm"
                              onClick={handleconfirmpassClick}
                            >
                              {confirmpass ? "Hide" : "Show"}
                            </Button>
                          </InputRightElement>
                        </InputGroup>
                      </FormControl>
                    </Box>
                  )}
                  <FormControl mb="10px">
                    <FormLabel>Roles</FormLabel>
                    <Input
                      placeholder={"Select Roles"}
                      onClick={(e) => {
                        addRolesHandler(e);
                      }}
                      value={query}
                      onChange={(e) => setQuery(e.target.value)}
                    />
                    {toggle && (
                      <Box
                        textAlign="left"
                        position={"absolute"}
                        zIndex={"1"}
                        bgColor={"white"}
                        width={"100%"}
                        border="1px"
                        borderColor="gray.300"
                        rounded={"md"}
                      >
                        <Flex width={"100%"} justifyContent={"flex-end"} py={2}>
                          <Button
                            size={"xs"}
                            colorScheme="whatsapp"
                            mx={2}
                            onClick={handlesaveSelectedRoles}
                          >
                            Save Changes
                          </Button>
                        </Flex>
                        <Flex
                          maxHeight={`${window.innerHeight - 400}px`}
                          overflowY={"scroll"}
                          overflowX={"hidden"}
                        >
                          <FormControl py={2} px={4} pt={0}>
                            {roles
                              .filter((e) =>
                                e.name
                                  .toLowerCase()
                                  .includes(query.toLocaleLowerCase())
                              )
                              .map((item, i) => {
                                return (
                                  <Fragment key={i}>
                                    <Checkbox
                                      isChecked={selectedRoles.some(
                                        (val) => val === item._id
                                      )}
                                      onChange={(e) => {
                                        if (e.target.checked === true) {
                                          let arr = [...selectedRoles];
                                          arr.push(item._id);
                                          setSelectedRoles(arr);
                                        } else if (e.target.checked === false) {
                                          let arr = [...selectedRoles];
                                          let newarr = arr.filter(
                                            (val) => val !== item._id
                                          );
                                          setSelectedRoles(newarr);
                                        }
                                      }}
                                    >
                                      {item.name}
                                    </Checkbox>
                                    <br />
                                  </Fragment>
                                );
                              })}
                          </FormControl>
                        </Flex>
                      </Box>
                    )}
                  </FormControl>
                  <Flex wrap={'wrap'}>
                    {showSelectedRoles.map((item, i) => (
                      <Badge
                        key={i}
                        colorScheme={i % 2 === 0 ? "purple" : "green"}
                        mr={3}
                      >
                        {roles.find((role) => item === role._id)?.name}
                      </Badge>
                    ))}
                  </Flex>
                </ModalBody>
                <ModalFooter gap="10px">
                  {updatebutton ? (
                    <Button
                      variant={"outline"}
                      colorScheme="green"
                      size={"sm"}
                      py={5}
                      px={4}
                      onClick={handleUpdateUserHitApi}
                    >
                      {updateloader ? <Spinner /> : "Update"}
                    </Button>
                  ) : (
                    <Button
                      variant={"outline"}
                      colorScheme="green"
                      size={"sm"}
                      py={5}
                      px={4}
                      onClick={handleCreateUserHitApi}
                    >
                      {createloader ? <Spinner /> : "Save"}
                    </Button>
                  )}
                  <Button
                    variant={"outline"}
                    colorScheme="red"
                    size={"sm"}
                    py={5}
                    px={4}
                    ml={1}
                    onClick={onClose}
                  >
                    Cancel
                  </Button>
                </ModalFooter>
              </ModalContent>
            </Modal>

            <ConfirmationModal
              heading="Delete User"
              action="Are you Sure? You want to Delete this User"
              ConfirmButton="Yes Delete"
              onClickFunction={handleDeleteUserButtonClickedApiHit}
              isOpen={confirmisOpen}
              onClose={closeConfirmModal}
              loader={true}
              loading={deleteloading}
            />

            <div
              style={{
                borderRadius: "7px",
                boxShadow:
                  "rgba(0, 0, 0, 0.05) 0px 6px 24px 0px, rgba(0, 0, 0, 0.08) 0px 0px 0px 1px",
                backgroundColor: "white",
              }}
              height="30px"
              width="30px"
            >
              <TableContainer
                height={`${window.innerHeight - 185}px`}
                overflowY={"scroll"}
              >
                <Table variant="simple">
                  <Thead
                    bgColor={"#c1eaee"}
                    position={"sticky"}
                    top={"0px"}
                    zIndex={"99"}
                  >
                    <Tr bgColor={"#E2DFDF"}>
                      <Th>S.No</Th>
                      <Th>Name</Th>
                      <Th>UserName</Th>
                      <Th>Role</Th>
                      <Th>action</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {user?.map((item, i) => (
                      <Tr key={i}>
                        <Td fontSize={"14px"}>{i + 1}.</Td>
                        <Td
                          style={{
                            whiteSpace: "pre-wrap",
                            wordWrap: "break-word",
                          }}
                          fontSize={"14px"}
                        >
                          {item.name}
                        </Td>
                        <Td
                          style={{
                            whiteSpace: "pre-wrap",
                            wordWrap: "break-word",
                          }}
                          fontSize={"14px"}
                        >
                          {item.username}
                        </Td>
                        <Td fontSize={"14px"}>
                          {item.userGroup.map((id, i) => (
                            <Box key={i}>
                              {item.userGroup.length - 1 === i ? (
                                <Badge
                                  colorScheme={i % 2 === 0 ? "purple" : "green"}
                                >
                                  {roles.find((role) => id === role._id)?.name}{" "}
                                </Badge>
                              ) : (
                                <Badge
                                  colorScheme={i % 2 === 1 ? "green" : "purple"}
                                  mb={1}
                                >
                                  {roles.find((role) => id === role._id)?.name}{" "}
                                </Badge>
                              )}
                            </Box>
                          ))}
                        </Td>
                        <Td fontSize={"14px"}>
                          {process.env.REACT_APP_SUPER_ADMIN_USER_ID !==
                            item._id && userData._id !== item._id ? (
                            <Flex>
                              {userData?.accessScopes?.user?.includes(
                                "write"
                              ) && (
                                <Flex
                                  onClick={() =>
                                    handleEditUserButtonClicked(item)
                                  }
                                  mr={1}
                                  cursor={"pointer"}
                                >
                                  <Tooltip label="Edit User">
                                    <Text>
                                      <MdEdit fontSize={"24px"} />
                                    </Text>
                                  </Tooltip>
                                </Flex>
                              )}
                              {userData?.accessScopes?.user?.includes(
                                "delete"
                              ) && (
                                <Flex
                                  cursor={"pointer"}
                                  onClick={() => openConfirmModal(item._id)}
                                >
                                  <Tooltip label="Delete User">
                                    <Text>
                                      <MdDelete fontSize={"24px"} />
                                    </Text>
                                  </Tooltip>
                                </Flex>
                              )}
                            </Flex>
                          ) : userData._id === item._id ? (
                            <Badge ariant="solid" colorScheme="teal">
                              Logged In
                            </Badge>
                          ) : null}
                        </Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              </TableContainer>
            </div>
          </Box>
        )}
      </Layout>
    </Box>
  );
};

export default AdminUsers;
