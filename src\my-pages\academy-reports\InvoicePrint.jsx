import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ody,
  ModalCloseButton,
  Button,
} from "@chakra-ui/react";
import React, { useState, useRef, useEffect } from "react";
import { Box, Flex, Heading, Text, VStack } from "@chakra-ui/react";
import { useReactToPrint } from "react-to-print";
import axios from "axios";
import { MdLocalPrintshop } from "react-icons/md";
import { numberToWords } from "../../utilities/convertIntoWords";

const InvoicePrint = ({ singleReport, invoiceModal, setInvoiceModal, singleReportIndex }) => {
  const [coach, setCoach] = useState({});

  const token = sessionStorage.getItem("admintoken").split(" ")[1];


  const invoiceRef = useRef();
  const handlePrint = useReactToPrint({
    content: () => invoiceRef.current,
  });

  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  function convertDateIntoIndianFormat(date) {
    const day = date.getDate();
    const month = months[date.getMonth()];
    const year = date.getFullYear();
    return `${day} ${month}, ${year}`;
  }

  const getCoachDetails = () => {
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/coach/${singleReport.coachId}`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        if (response.data.error) {
          console.log(response.data.error);
        } else {
          setCoach(response?.data);
        }
      })
      .catch((error) => {
        console.log(error);
      });
  };

  useEffect(() => {
    getCoachDetails();
  }, []);

  return (
    <>
      <Modal isOpen={invoiceModal} onClose={false} size="full">
        <ModalOverlay />
        <ModalContent maxW="70%">
          <ModalCloseButton onClick={() => setInvoiceModal(!invoiceModal)} />
          <ModalBody>
            <Box p={10} ref={invoiceRef}>
              <Heading as="h1" size="lg" display="flex" flexDirection="row">
                {coach?.firstName}
                <Text as="span" ml={2} color="pink.300">
                  {coach?.lastName}
                </Text>
              </Heading>
              <Flex justifyContent="space-between" my={10}>
                <VStack align="start" spacing={1}>
                  <Text fontWeight="bold" fontSize="sm">
                    To,
                  </Text>
                  <Text fontWeight="bold" fontSize="sm">
                    Umn Khel Shiksha Private Limited
                  </Text>
                  <Text fontSize="sm">Vasant Vihar</Text>
                  <Text fontSize="sm">Basant Lok Complex, Road 21</Text>
                  <Text fontSize="sm">New Delhi-110057</Text>
                  <Text fontSize="sm" mt={2}>
                    GST ID: 07AADCU2822L1Z8
                  </Text>
                </VStack>
                <VStack align="start" spacing={1}>
                  <Flex>
                    <Text fontSize="sm" fontWeight="bold">
                      Date:
                    </Text>
                    <Text fontSize="sm" ml={2}>
                      {convertDateIntoIndianFormat(
                        new Date(singleReport?.date.split("T")[0])
                      )}
                    </Text>
                  </Flex>
                  <Flex>
                    <Text fontSize="sm" fontWeight="bold">
                      Invoice No.:
                    </Text>
                    <Text fontSize="sm" ml={2}>
                      {singleReport?.bookingId}_{singleReportIndex + 1}
                    </Text>
                  </Flex>
                </VStack>
              </Flex>

              <Box mb={10}>
                <Flex justifyContent="space-between">
                  <Text
                    fontSize="sm"
                    fontWeight="light"
                    textDecoration="underline"
                  >
                    DESCRIPTION
                  </Text>
                  <Text
                    fontSize="sm"
                    fontWeight="light"
                    textDecoration="underline"
                  >
                    AMOUNT
                  </Text>
                </Flex>
                <Box mt={2}>
                  <Flex justifyContent="space-between">
                    <Text fontSize="sm">{singleReport?.courseName}</Text>
                    <Text fontSize="sm">
                      ₹{singleReport?.coachFeesAfterCancelation?.toFixed(2)}
                    </Text>
                  </Flex>
                  <Flex justifyContent="space-between">
                    <Text fontSize="sm">TDS Deduction (1%)</Text>
                    <Text fontSize="sm">₹{singleReport?.tds?.toFixed(2)}</Text>
                  </Flex>
                  <Flex justifyContent="space-between" mt={2}>
                    <Text fontSize="sm" fontWeight="light">
                      Total:(round of)
                    </Text>
                    <Text fontSize="sm" fontWeight="light">
                      ₹{singleReport?.amountReceived?.toFixed(2)}
                    </Text>
                  </Flex>
                </Box>
              </Box>

              <Box mb={10}>
                <Text fontSize="sm">
                  Amount (in words): Rupees{" "}
                  {numberToWords(singleReport?.amountReceived?.toFixed(2))}{" "}
                  only.
                </Text>
              </Box>

              <Box mb={10}>
                <Text fontSize="sm" fontWeight="bold">
                  Payment Details:
                </Text>
                <Text fontSize="sm">
                  Pan card no - {coach?.kycDocuments?.documentNumber}
                  <br />
                  Beneficiary name - {coach?.bankDetails?.accountHolderName}
                  <br />
                  Account no - {coach?.bankDetails?.accountNumber}
                  <br />
                  IFSC code - {coach?.bankDetails?.ifsc}
                </Text>
              </Box>

              <Box mb={10}>
                <Text fontSize="sm">
                  Regards,
                  <br /> {coach?.firstName} {coach?.lastName}
                </Text>
              </Box>
            </Box>
          </ModalBody>

          <ModalFooter>
            <Button
              variant="link"
              colorScheme="red"
              mr={3}
              onClick={() => setInvoiceModal(!invoiceModal)}
            >
              Close
            </Button>

            <Button
              ml={"3rem"}
              leftIcon={<MdLocalPrintshop />}
              colorScheme="blue"
              variant="link"
              onClick={handlePrint}
            >
              Print
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
};

export default InvoicePrint;
