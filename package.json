{"name": "NioBoardReact", "version": "1.0.0", "private": true, "dependencies": {"@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fullcalendar/bootstrap": "^5.11.3", "@fullcalendar/daygrid": "^5.11.3", "@fullcalendar/interaction": "^5.11.3", "@fullcalendar/list": "^5.11.3", "@fullcalendar/react": "^5.11.2", "@fullcalendar/timegrid": "^5.11.3", "@popperjs/core": "^2.11.6", "@reduxjs/toolkit": "^2.2.3", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "@tinymce/tinymce-react": "^4.2.0", "axios": "^1.6.2", "bootstrap": "^5.2.0", "chart.js": "^3.9.1", "choices.js": "^10.1.0", "classnames": "^2.3.1", "country-state-city": "^3.2.1", "export-from-json": "^1.7.0", "formik": "^2.4.5", "framer-motion": "^10.16.12", "html-react-parser": "^3.0.4", "jwt-decode": "^4.0.0", "moment-timezone": "^0.5.45", "nouislider": "^15.6.1", "react": "^18.2.0", "react-bootstrap": "^2.5.0", "react-chartjs-2": "^4.3.1", "react-copy-to-clipboard": "^5.1.0", "react-data-table-component": "^7.5.3", "react-datepicker": "^6.9.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-helmet": "^6.1.0", "react-icons": "^4.12.0", "react-paginate": "^8.2.0", "react-quill": "^2.0.0", "react-quilljs": "^1.3.3", "react-redux": "^9.1.0", "react-router-dom": "^6.4.2", "react-scripts": "5.0.1", "react-syntax-highlighter": "^15.5.0", "react-to-print": "^2.15.1", "redux-persist": "^6.0.0", "sass": "^1.54.5", "simplebar-react": "^2.4.1", "svgmap": "^2.10.1", "sweetalert2": "^11.4.33", "sweetalert2-react-content": "^5.0.3", "vanillajs-datepicker": "^1.2.0", "web-vitals": "^2.1.4", "yup": "^1.4.0"}, "scripts": {"start": "react-scripts start --port 3002", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}