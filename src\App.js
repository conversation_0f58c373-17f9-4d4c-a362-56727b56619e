import { useEffect } from "react";
import Router from "./router";
import { useNavigate } from "react-router-dom";
import { jwtDecode } from "jwt-decode";
import { useToast } from "@chakra-ui/react";
import { useState } from "react";
function App() {
  const [show , setShow] = useState(false);
  const toast = useToast();
  const navigate = useNavigate();
  const token = sessionStorage.getItem("admintoken");

  useEffect(() => {
    const checkTokenValidity = () => {
      if (!token) {
        setShow(true);
        navigate("/login");
      } else {
        try {
          const decodedToken = jwtDecode(token.split(" ")[1]);
          const expirationTime = decodedToken.exp * 1000; // Convert seconds to milliseconds
          const currentTime = Date.now();
          
          // Check if the token has expired
          if (expirationTime < currentTime) {
            setShow(false);
            toast({
              title: "Session has expired, please login again",
              status: "warning",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
            sessionStorage.removeItem("admintoken");
            navigate("/login");
          }else{
            setShow(true);
          }
        } catch (error) {
          // Handle decoding errors
          console.error("Error decoding token:", error);
          sessionStorage.removeItem("admintoken");
          navigate("/login");
          setShow(true);
        }
      }
    };

    checkTokenValidity();
  }, [navigate, token]);

  return (
    <>
      {show && <Router auth={token} />}
    </>
  );
}

export default App;
