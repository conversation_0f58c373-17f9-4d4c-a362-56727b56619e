import React, { useEffect, useState } from "react";
import Layout from "../../layout/default";
import { Link, useLocation, useNavigate } from "react-router-dom";
import ReactPaginate from "react-paginate";
import {
  BreadcrumbLink,
  Breadcrumb,
  BreadcrumbItem,
  Text,
  Flex,
  TableContainer,
  Table,
  Tbody,
  Tr,
  Td,
  Thead,
  Th,
  Spinner,
  Tooltip,
  useToast,
  Box,
  InputGroup,
  Input,
  InputRightAddon,
  Select,
  Popover,
  PopoverTrigger,
  PopoverContent,
  PopoverArrow,
  PopoverCloseButton,
  PopoverBody,
  Button,
  PopoverHeader,
  FormControl,
  FormLabel,
  Badge,
} from "@chakra-ui/react";
import axios from "axios";
import { IoIosClose, IoMdSearch } from "react-icons/io";
import { IoFilter } from "react-icons/io5";

const Booking = () => {
  const [bookingData, setBookingData] = useState({
    result: [],
    isLoading: false,
    error: false,
    notFound: false,
  });

  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [showSearch, setShowSearch] = useState(false);
  const [selectedStartDate, setSelectedStartDate] = useState("");
  const [selectedEndDate, setSelectedEndDate] = useState("");
  const [selectedClassType, setSelectedClassType] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");
  const [searchCourseName, setSearchCourseName] = useState("");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const [startDateError, setStartDateError] = useState(false);
  const [endDateError, setEndDateError] = useState(false);

  const token = sessionStorage?.getItem("admintoken")?.split(" ")[1];
  const toast = useToast();
  const location = useLocation();

  const [filters, setFilters] = useState({
    startDate: "",
    endDate: "",
  });

  const navigate = useNavigate();

  useEffect(() => {
    const newParams = new URLSearchParams();
    Object.keys(filters).forEach((key) => {
      if (filters[key]) {
        newParams.append(key, filters[key]);
      }
    });
    navigate(`?${newParams.toString()}`);
  }, [filters, navigate]);

  const handleFilterChange = (event) => {
    const { name, value } = event.target;
    if (name === "startDate") {
      setStartDateError(false);
    }
    if (name === "endDate") {
      setEndDateError(false);
    }
    setFilters((prevFilters) => ({
      ...prevFilters,
      [name]: value,
    }));
  };

  const getBookingData = (
    startDate,
    endDate,
    courseType,
    status,
    courseName
  ) => {
    setBookingData({
      result: [],
      isLoading: true,
      error: false,
      notFound: false,
    });
    let queryString = "?";

    if (courseName) {
      queryString += `courseName=${courseName}`;
    } else {
      queryString = `?page=${currentPage}`;
    }

    if (startDate) {
      queryString += `${queryString ? "&" : ""}startDate=${startDate}`;
    }

    if (endDate) {
      queryString += `${queryString ? "&" : ""}endDate=${endDate}`;
    }

    if (courseType) {
      queryString += `${queryString ? "&" : ""}courseType=${courseType}`;
    }

    if (status) {
      queryString += `${queryString ? "&" : ""}status=${status}`;
    }

    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/booking/${
        queryString ? `${queryString}` : ""
      }`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };
    axios
      .request(config)
      .then((response) => {
        setBookingData({
          result: response.data,
          isLoading: false,
          error: false,
          notFound: response.data.message === "No bookings found",
        });
        setTotalPages(Math.ceil(response.data.totalResults / 25));
      })
      .catch((error) => {
        console.log(error);
        setBookingData({
          result: [],
          isLoading: false,
          error: true,
          notFound: false,
        });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const handlePageChange = ({ selected }) => {
    setCurrentPage(selected + 1);
  };

  const formatDate = (date) => {
    let originalDate = new Date(date);

    let day = ("0" + originalDate.getDate()).slice(-2);
    let month = ("0" + (originalDate.getMonth() + 1)).slice(-2);
    let year = originalDate.getFullYear();

    let desiredDateFormat = day + "-" + month + "-" + year;

    return desiredDateFormat;
  };

  useEffect(() => {
    getBookingData(
      startDate,
      endDate,
      selectedClassType,
      selectedStatus,
      searchCourseName
    );
  }, [
    currentPage,
    selectedClassType,
    selectedStatus,
    searchCourseName,
    startDate,
    endDate,
  ]);

  return (
    <Layout title="Booking" content="container">
      <Flex
        w={"100%"}
        justifyContent={"space-between"}
        alignItems={"center"}
        mb={6}
      >
        {showSearch ? (
          <Box flexBasis={"58%"}>
            <InputGroup size="md">
              <Input
                pr="4.5rem"
                type="text"
                placeholder="Search by course name"
                borderColor={"gray.300"}
                onChange={(e) => {
                  if (e.target.value.length >= 3) {
                    setTimeout(() => {
                      setSearchCourseName(e.target.value);
                    }, 500);
                  }
                  if (e.target.value.length === 0) {
                    setSearchCourseName("");
                  }
                }}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    if (e.target.value.length >= 3) {
                      setSearchCourseName(e.target.value);
                    }
                  }
                }}
              />
              <InputRightAddon
                bgColor={"gray.300"}
                border={"1px"}
                borderColor={"gray.300"}
                onClick={() => {
                  setShowSearch(false);
                  setSearchCourseName("");
                }}
                cursor={"pointer"}
              >
                <IoIosClose fontSize={"24px"} />
              </InputRightAddon>
            </InputGroup>
          </Box>
        ) : (
          <Flex
            flexBasis={"59%"}
            justifyContent={"space-between"}
            alignItems={"center"}
          >
            <Breadcrumb fontWeight="medium" fontSize="sm">
              <BreadcrumbItem>
                <Link to={"/"}>Dashboard</Link>
              </BreadcrumbItem>

              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">Bookings</BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>
            <Text
              display={"flex"}
              px={4}
              py={"7px"}
              border={"1px"}
              borderColor={"gray.300"}
              rounded={"md"}
              color="gray.500"
              cursor={"pointer"}
              onClick={() => setShowSearch(true)}
            >
              <IoMdSearch fontSize={"24px"} />
              <IoFilter fontSize={"22px"} ml={1} />
            </Text>
          </Flex>
        )}
        <Flex
          flexBasis={"40%"}
          justifyContent={"space-between"}
          alignItems={"center"}
        >
          <Box flexBasis={"31%"} position="relative">
            <Popover isOpen={isOpen}>
              <PopoverTrigger>
                <Text
                  display={"flex"}
                  justifyContent={"center"}
                  alignItems={"center"}
                  mb={0}
                  position={"relative"}
                  py={"7px"}
                  border={"1px"}
                  borderColor={"gray.300"}
                  rounded={"md"}
                  color="gray.700"
                  cursor={"pointer"}
                  textAlign={"center"}
                  bgColor={startDate && endDate && "gray.300"}
                  onClick={() => {
                    setIsOpen(true);
                    setStartDateError(false);
                    setEndDateError(false);
                  }}
                >
                  Date Range
                </Text>
              </PopoverTrigger>
              <PopoverContent border={"1px"} borderColor={"gray.300"}>
                <PopoverArrow />
                <PopoverCloseButton
                  mt={1}
                  onClick={() => {
                    setIsOpen(false);
                    setStartDateError(false);
                    setEndDateError(false);
                  }}
                />
                <PopoverHeader fontWeight={"semibold"}>
                  Select Date Range
                </PopoverHeader>
                <PopoverBody zIndex={"99"} bgColor={"F5F5F5"}>
                  <Box mb={2}>
                    <FormControl isInvalid={startDateError}>
                      <FormLabel>Start Date</FormLabel>
                      <InputGroup size={"md"}>
                        <Input
                          type="date"
                          max={
                            selectedEndDate ||
                            new Date().toISOString().split("T")[0]
                          }
                          value={filters.startDate}
                          name="startDate"
                          onChange={handleFilterChange}
                        />
                        <InputRightAddon
                          bgColor={"gray.300"}
                          border={"1px"}
                          borderColor={"gray.300"}
                          cursor={"pointer"}
                          onClick={() => {
                            setStartDate("");
                            setFilters((prevFilters) => ({
                              ...prevFilters,
                              startDate: "",
                            }));
                          }}
                        >
                          <IoIosClose fontSize={"24px"} />
                        </InputRightAddon>
                      </InputGroup>
                      {startDateError && (
                        <Text color={"red.500"} fontSize={"0.9rem"}>
                          Enter start date
                        </Text>
                      )}
                    </FormControl>
                    <FormControl mt={2} isInvalid={endDateError}>
                      <FormLabel>End Date</FormLabel>
                      <InputGroup size={"md"}>
                        <Input
                          type="date"
                          min={selectedStartDate}
                          max={new Date().toISOString().split("T")[0]}
                          value={filters.endDate}
                          name="endDate"
                          onChange={handleFilterChange}
                        />
                        <InputRightAddon
                          bgColor={"gray.300"}
                          border={"1px"}
                          borderColor={"gray.300"}
                          cursor={"pointer"}
                          onClick={() => {
                            setEndDate("");
                            setFilters((prevFilters) => ({
                              ...prevFilters,
                              endDate: "",
                            }));
                          }}
                        >
                          <IoIosClose fontSize={"24px"} />
                        </InputRightAddon>
                      </InputGroup>
                      {endDateError && (
                        <Text color={"red.500"} fontSize={"0.9rem"}>
                          Enter start date
                        </Text>
                      )}
                    </FormControl>
                    <Button
                      colorScheme="telegram"
                      size={"sm"}
                      w={"full"}
                      mt={4}
                      isDisabled={!filters.startDate && !filters.endDate}
                      onClick={() => {
                        if (!filters.startDate) {
                          setStartDateError(true);
                          return;
                        }
                        if (!filters.endDate) {
                          setEndDateError(true);
                        }
                        if (filters.startDate && filters.endDate) {
                          setIsOpen(false);
                          setStartDate(filters.startDate);
                          setEndDate(filters.endDate);
                        }
                      }}
                    >
                      Apply
                    </Button>
                  </Box>
                </PopoverBody>
              </PopoverContent>
            </Popover>
            {/* Rounded badge container */}
            {(startDate || endDate) && (
              <Tooltip label={"Remove filter"}>
                <Box
                  position="absolute"
                  top={-1}
                  right={-1}
                  bgColor="gray.600"
                  borderRadius="full"
                  p={1}
                  cursor={"pointer"}
                  onClick={() => {
                    setFilters({
                      startDate: "",
                      endDate: "",
                    });
                    setStartDate("");
                    setEndDate("");
                  }}
                >
                  <IoIosClose fontSize={"10px"} color="white" />
                </Box>
              </Tooltip>
            )}
          </Box>
          <Box flexBasis={"33%"}>
            <Select
              placeholder="Class Type"
              borderColor={"gray.300"}
              bgColor={selectedClassType && "gray.300"}
              cursor={"pointer"}
              onChange={(e) => {
                setSelectedClassType(e.target.value);
                setCurrentPage(1);
              }}
            >
              <option value="class">Class</option>
              <option value="course">Course</option>
              <option value="">All</option>
            </Select>
          </Box>
          <Box flexBasis={"31%"}>
            <Select
              placeholder="Status"
              borderColor={"gray.300"}
              bgColor={selectedStatus && "gray.300"}
              cursor={"pointer"}
              onChange={(e) => {
                setSelectedStatus(e.target.value);
                setCurrentPage(1);
              }}
            >
              <option value="Pending">Active</option>
              <option value="Completed">Completed</option>
              <option value="">All</option>
            </Select>
          </Box>
        </Flex>
      </Flex>
      {/* Added/Selected Course List */}
      {!bookingData?.isLoading && bookingData?.error ? (
        <Flex
          justifyContent={"center"}
          alignItems={"center"}
          w={"full"}
          my={10}
        >
          <Text color={"red.500"}>
            Something went wrong please try again later...
          </Text>
        </Flex>
      ) : (
        <>
          <TableContainer
            mt={6}
            height={`${window.innerHeight - 235}px`}
            overflowY={"scroll"}
          >
            <Table variant="simple">
              <Thead bgColor={"#c1eaee"} position={"sticky"} top={"0px"}>
                <Tr bgColor={"#E2DFDF"}>
                  <Th>Booking Id</Th>
                  <Th>Training Schedule</Th>
                  <Th>Customer</Th>
                  <Th>Type</Th>
                  <Th>Amount</Th>
                  <Th>Date</Th>
                  <Th>Status</Th>
                </Tr>
              </Thead>
              <Tbody>
                {bookingData?.isLoading && !bookingData?.error ? (
                  <Tr>
                    {/* <Td></Td> */}
                    <Td></Td>
                    <Td></Td>
                    <Td></Td>
                    <Td
                      display={"flex"}
                      justifyContent={"center"}
                      alignItems={"center"}
                    >
                      <Spinner />
                    </Td>
                    <Td></Td>
                    <Td></Td>
                    <Td></Td>
                  </Tr>
                ) : !bookingData?.notFound ? (
                  bookingData?.result?.data?.map((bookData, inx) => {
                    return (
                      <Tr
                        cursor={"pointer"}
                        key={bookData._id}
                        onClick={() =>
                          navigate(`/Booking/details/${bookData._id}`)
                        }
                      >
                        <Td fontSize={"14px"}>
                          {bookData?.bookingId}
                        </Td>
                        <Td
                          style={{
                            whiteSpace: "pre-wrap",
                            wordWrap: "break-word",
                          }}
                          fontSize={"14px"}
                        >
                          {bookData?.courseName || "n/a"}
                        </Td>
                        <Td
                          style={{
                            whiteSpace: "pre-wrap",
                            wordWrap: "break-word",
                          }}
                          fontSize={"14px"}
                        >
                          {bookData?.playerName || "n/a"}
                        </Td>
                        <Td fontSize={"14px"}>
                          {bookData?.courseType?.charAt(0).toUpperCase() +
                            bookData?.courseType?.slice(1) || "n/a"}{" "}
                        </Td>
                        <Td fontSize={"14px"}>
                          &#8377;{bookData?.pricePaid?.toFixed(2) || "n/a"}{" "}
                        </Td>
                        <Td fontSize={"14px"}>
                          {formatDate(bookData?.createdAt) || "n/a"}{" "}
                        </Td>
                        <Td
                          style={{
                            whiteSpace: "pre-wrap",
                            wordWrap: "break-word",
                          }}
                          px={4}
                          fontSize={"14px"}
                        >
                          <Badge
                            colorScheme={
                              bookData?.status?.toLowerCase() === "active"
                                ? "green"
                                : "red"
                            }
                          >
                            {bookData?.status}
                          </Badge>
                        </Td>
                      </Tr>
                    );
                  })
                ) : (
                  <Tr>
                    <Td></Td>
                    <Td></Td>
                    <Td></Td>
                    <Td
                      display={"flex"}
                      justifyContent={"center"}
                      alignItems={"center"}
                    >
                      <Text color={"green.500"} fontWeight={"semibold"}>
                        No result found
                      </Text>
                    </Td>
                    <Td></Td>
                    <Td></Td>
                    <Td></Td>
                  </Tr>
                )}
              </Tbody>
            </Table>
          </TableContainer>
          {/* Pagination */}
          {!bookingData?.notFound && (
            <Flex
              justifyContent="center"
              alignItems="center"
              flexDirection={"row"}
              w={"100%"}
              mt={5}
            >
              <ReactPaginate
                previousLabel="Previous"
                nextLabel="Next"
                breakLabel="..."
                pageCount={totalPages}
                marginPagesDisplayed={2}
                pageRangeDisplayed={5}
                onPageChange={handlePageChange}
                containerClassName="pagination"
                activeClassName="active"
                forcePage={currentPage - 1}
              />
            </Flex>
          )}
        </>
      )}
    </Layout>
  );
};

export default Booking;
