import React, { useEffect, useState } from "react";
import {
  Avatar,
  Button,
  Card,
  CardBody,
  Divider,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  useToast,
  FormErrorMessage,
  Box,
  ButtonGroup,
  Fade,
  ScaleFade,
  useColorModeValue,
  Icon,
  VStack,
  HStack,
  Tooltip,
  Badge,
} from "@chakra-ui/react";
import { LuCheck, LuPencilLine, LuX, LuUser, LuMail, LuPhone, LuCamera, LuDollarSign } from "react-icons/lu";
import { useFormik } from "formik";
import * as Yup from "yup";
import axios from "axios";

const BasicDetailsAcademy = ({ academyData }) => {
  
  const [profileImagePreview, setProfileImagePreview] = useState("");
  const [isSubmitBtnLoading, setIsSubmitBtnLoading] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  
  const toast = useToast();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];

  // Color mode values for better theming
  const cardBg = useColorModeValue("white", "gray.800");
  const readOnlyBg = useColorModeValue("gray.50", "gray.700");
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const textColor = useColorModeValue("gray.700", "gray.200");
  const labelColor = useColorModeValue("gray.600", "gray.300");

  useEffect(() => {
    if (academyData?.profileImage) {
      setProfileImagePreview(academyData.profileImage);
    }
  }, [academyData]);

  const handleProfileImageChange = async (e) => {
    try {
      const file = e.currentTarget.files[0];
      if (file && file.size > 10 * 1024 * 1024) {
        toast({
          title: "Please select a file less than 10 MB",
          status: "warning",
          duration: 5000,
          position: "top",
          isClosable: true,
        });
        return;
      }
      const formData = new FormData();
      formData.append("image", file);

      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/api/academy/uploadImage`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      const url = response?.data?.url;

      if (url) {
        toast({
          title: "Profile image uploaded",
          status: "success",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
        e.target.value = null;
      } else {
        toast({
          title: "Something went wrong while uploading profile image",
          status: "error",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
        e.target.value = null;
      }
      formik.setFieldValue("profileImg", url);
      setProfileImagePreview(url);
    } catch (error) {
      console.log(error);
      toast({
        title: "Something went wrong please try again later",
        status: "error",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    }
  };

  const validationSchema = Yup.object().shape({
    academyName: Yup.string()
      .required("Academy name is required")
      .min(3, "Academy name must be at least 3 characters")
      .max(50, "Academy name must be less than or equal to 50 characters"),
    platformFee: Yup.string()
      .required("Platform fee is required")
      .min(1, "Platform fee must be at least 1 character")
      .max(50, "Platform fee must be less than or equal to 50 characters"),
  });

  const formik = useFormik({
    initialValues: {
      academyName: academyData?.name || "",
      email: academyData?.email || "",
      phoneNumber: academyData?.mobile || "",
      platformFee: academyData?.platformFee || "",
      profileImg: academyData?.profileImage || "",
    },
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      setIsSubmitBtnLoading(true);
      try {
        const payload = {
          name: values.academyName,
          platformFee: values.platformFee,
          profileImage: values.profileImg,
        };
        
        const response = await axios.patch(
          `${process.env.REACT_APP_BASE_URL}/api/academy/${academyData?._id}`,
          payload,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.data.status === "success") {
          toast({
            title: "Academy updated successfully",
            status: "success",
            duration: 3000,
            position: "top",
            isClosable: true,
          });
          setIsEditMode(false);
        } else {
          toast({
            title: response.data.message || "Something went wrong",
            status: "error",
            duration: 3000,
            position: "top",
            isClosable: true,
          });
        }
      } catch (error) {
        console.log(error);
        if (error.response?.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      } finally {
        setIsSubmitBtnLoading(false);
      }
    },
  });

  useEffect(() => {
    if (academyData?.profileImage) {
      setProfileImagePreview(academyData.profileImage);
    }
  }, [academyData]);

  const handleEdit = () => {
    setIsEditMode(true);
  };

  const handleSave = () => {
    formik.handleSubmit();
  };

  const handleCancel = () => {
    formik.resetForm();
    setIsEditMode(false);
  };

  return (
    <ScaleFade initialScale={0.9} in={true}>
      <Card 
        bg={cardBg}
        shadow="xl"
        borderRadius={{ base: "lg", md: "xl" }}
        border="1px"
        borderColor={borderColor}
        overflow="hidden"
        transition="all 0.3s ease"
        w="full"
        _hover={{
          shadow: "2xl",
          transform: "translateY(-2px)"
        }}
      >
        <CardBody p={{ base: 4, md: 8 }}>
          <Flex 
            justifyContent="space-between" 
            alignItems={{ base: "flex-start", md: "center" }} 
            direction={{ base: "column", md: "row" }}
            gap={{ base: 4, md: 0 }}
            mb={{ base: 4, md: 6 }}
          >
            <HStack spacing={3}>
              <Icon as={LuUser} boxSize={{ base: 5, md: 6 }} color="blue.500" />
              <Heading size={{ base: "md", md: "lg" }} color={textColor} fontWeight="bold">
                Basic Details
              </Heading>
            </HStack>
            <Fade in={true}>
              {!isEditMode ? (
                <Tooltip label="Edit profile information" placement="top">
                  <Button
                    colorScheme="blue"
                    variant="outline"
                    onClick={handleEdit}
                    leftIcon={<LuPencilLine />}
                    size={{ base: "sm", md: "md" }}
                    w={{ base: "full", md: "auto" }}
                    transition="all 0.2s"
                    _hover={{
                      transform: "scale(1.05)",
                      shadow: "md"
                    }}
                  >
                    Edit
                  </Button>
                </Tooltip>
              ) : (
                <ButtonGroup spacing={{ base: 2, md: 3 }} w={{ base: "full", md: "auto" }}>
                  <Button
                    colorScheme="green"
                    onClick={handleSave}
                    isLoading={isSubmitBtnLoading}
                    leftIcon={<LuCheck />}
                    size={{ base: "sm", md: "md" }}
                    flex={{ base: 1, md: "none" }}
                    transition="all 0.2s"
                    _hover={{
                      transform: "scale(1.05)",
                      shadow: "md"
                    }}
                  >
                    Save
                  </Button>
                  <Button
                    variant="outline"
                    colorScheme="red"
                    onClick={handleCancel}
                    leftIcon={<LuX />}
                    size={{ base: "sm", md: "md" }}
                    flex={{ base: 1, md: "none" }}
                    transition="all 0.2s"
                    _hover={{
                      transform: "scale(1.05)",
                      shadow: "md"
                    }}
                  >
                    Cancel
                  </Button>
                </ButtonGroup>
              )}
            </Fade>
          </Flex>
          
          <Divider borderColor={borderColor} mb={{ base: 6, md: 8 }} />
          
          <VStack spacing={{ base: 6, md: 8 }} align="stretch">
            {/* Profile Image Section */}
            <FormControl>
              <HStack spacing={3} mb={{ base: 3, md: 4 }}>
                <Icon as={LuCamera} boxSize={5} color="purple.500" />
                <FormLabel fontWeight="semibold" color={labelColor} mb={0} fontSize={{ base: "sm", md: "md" }}>
                  Profile Image
                </FormLabel>
              </HStack>
              <Flex direction="column" align="center" gap={{ base: 3, md: 4 }}>
                <Box position="relative">
                  <Avatar 
                    size={{ base: "xl", md: "2xl" }}
                    src={profileImagePreview}
                    border="4px solid"
                    borderColor="blue.100"
                    shadow="lg"
                    transition="all 0.3s ease"
                    _hover={{
                      borderColor: "blue.300",
                      shadow: "xl"
                    }}
                  />
                  {isEditMode && (
                    <Badge
                      position="absolute"
                      top={-2}
                      right={-2}
                      colorScheme="blue"
                      borderRadius="full"
                      p={1}
                    >
                      <Icon as={LuCamera} boxSize={3} />
                    </Badge>
                  )}
                </Box>
                {isEditMode && (
                  <Fade in={isEditMode}>
                    <VStack spacing={3}>
                      <Input
                        type="file"
                        id="profileImageInput"
                        onChange={handleProfileImageChange}
                        style={{ display: "none" }}
                        accept="image/*"
                      />
                      <Button 
                        onClick={() => document.getElementById('profileImageInput').click()} 
                        colorScheme="purple"
                        size={{ base: "sm", md: "md" }}
                        variant="outline"
                        borderRadius="full"
                        leftIcon={<LuCamera />}
                        width={{ base: "full", md: "auto" }}
                        transition="all 0.2s"
                        _hover={{
                          transform: "scale(1.05)",
                          shadow: "md"
                        }}
                      >
                        Upload New Image
                      </Button>
                    </VStack>
                  </Fade>
                )}
              </Flex>
            </FormControl>
            
            {/* Academy Name Section */}
            <FormControl isInvalid={formik.errors.academyName && formik.touched.academyName}>
              <HStack spacing={3} mb={{ base: 3, md: 4 }}>
                <Icon as={LuUser} boxSize={5} color="blue.500" />
                <FormLabel fontWeight="semibold" color={labelColor} mb={0} fontSize={{ base: "sm", md: "md" }}>
                  Academy Name
                </FormLabel>
              </HStack>
              <Fade in={true}>
                {isEditMode ? (
                  <Input
                    type="text"
                    name="academyName"
                    value={formik.values.academyName}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter academy name"
                    size={{ base: "md", md: "lg" }}
                    borderRadius={{ base: "lg", md: "xl" }}
                    border="2px"
                    borderColor={borderColor}
                    transition="all 0.2s"
                    _hover={{
                      borderColor: "blue.300"
                    }}
                    _focus={{
                      borderColor: "blue.500",
                      shadow: "0 0 0 1px var(--chakra-colors-blue-500)"
                    }}
                  />
                ) : (
                  <Box
                    p={{ base: 3, md: 4 }}
                    borderWidth="2px"
                    borderRadius={{ base: "lg", md: "xl" }}
                    bg={readOnlyBg}
                    color={textColor}
                    minH={{ base: "44px", md: "48px" }}
                    display="flex"
                    alignItems="center"
                    borderColor={borderColor}
                    fontSize={{ base: "md", md: "lg" }}
                    fontWeight="medium"
                    transition="all 0.2s"
                    _hover={{
                      shadow: "sm"
                    }}
                  >
                    {formik.values.academyName || "No academy name provided"}
                  </Box>
                )}
              </Fade>
              <FormErrorMessage fontSize={{ base: "xs", md: "sm" }}>{formik.errors.academyName}</FormErrorMessage>
            </FormControl>
            
            {/* Email Section */}
            <FormControl>
              <HStack spacing={3} mb={{ base: 3, md: 4 }}>
                <Icon as={LuMail} boxSize={5} color="green.500" />
                <FormLabel fontWeight="semibold" color={labelColor} mb={0} fontSize={{ base: "sm", md: "md" }}>
                  Email Address
                </FormLabel>
              </HStack>
              <Fade in={true}>
                <Box
                  p={{ base: 3, md: 4 }}
                  borderWidth="2px"
                  borderRadius={{ base: "lg", md: "xl" }}
                  bg={readOnlyBg}
                  color={textColor}
                  minH={{ base: "44px", md: "48px" }}
                  display="flex"
                  alignItems="center"
                  borderColor={borderColor}
                  fontSize={{ base: "md", md: "lg" }}
                  fontWeight="medium"
                  transition="all 0.2s"
                  _hover={{
                    shadow: "sm"
                  }}
                >
                  {formik.values.email || "No email provided"}
                </Box>
              </Fade>
            </FormControl>
            
            {/* Phone Number Section */}
            <FormControl>
              <HStack spacing={3} mb={{ base: 3, md: 4 }}>
                <Icon as={LuPhone} boxSize={5} color="orange.500" />
                <FormLabel fontWeight="semibold" color={labelColor} mb={0} fontSize={{ base: "sm", md: "md" }}>
                  Phone Number
                </FormLabel>
              </HStack>
              <Fade in={true}>
                <Box
                  p={{ base: 3, md: 4 }}
                  borderWidth="2px"
                  borderRadius={{ base: "lg", md: "xl" }}
                  bg={readOnlyBg}
                  color={textColor}
                  minH={{ base: "44px", md: "48px" }}
                  display="flex"
                  alignItems="center"
                  borderColor={borderColor}
                  fontSize={{ base: "md", md: "lg" }}
                  fontWeight="medium"
                  transition="all 0.2s"
                  _hover={{
                    shadow: "sm"
                  }}
                >
                  {formik.values.phoneNumber || "No phone number provided"}
                </Box>
              </Fade>
            </FormControl>

            {/* Platform Fee Section */}
            <FormControl isInvalid={formik.errors.platformFee && formik.touched.platformFee}>
              <HStack spacing={3} mb={{ base: 3, md: 4 }}>
                <Icon as={LuDollarSign} boxSize={5} color="teal.500" />
                <FormLabel fontWeight="semibold" color={labelColor} mb={0} fontSize={{ base: "sm", md: "md" }}>
                  Platform Fee
                </FormLabel>
              </HStack>
              <Fade in={true}>
                {isEditMode ? (
                  <Input
                    type="text"
                    name="platformFee"
                    value={formik.values.platformFee}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter platform fee"
                    size={{ base: "md", md: "lg" }}
                    borderRadius={{ base: "lg", md: "xl" }}
                    border="2px"
                    borderColor={borderColor}
                    transition="all 0.2s"
                    _hover={{
                      borderColor: "teal.300"
                    }}
                    _focus={{
                      borderColor: "teal.500",
                      shadow: "0 0 0 1px var(--chakra-colors-teal-500)"
                    }}
                  />
                ) : (
                  <Box
                    p={{ base: 3, md: 4 }}
                    borderWidth="2px"
                    borderRadius={{ base: "lg", md: "xl" }}
                    bg={readOnlyBg}
                    color={textColor}
                    minH={{ base: "44px", md: "48px" }}
                    display="flex"
                    alignItems="center"
                    borderColor={borderColor}
                    fontSize={{ base: "md", md: "lg" }}
                    fontWeight="medium"
                    transition="all 0.2s"
                    _hover={{
                      shadow: "sm"
                    }}
                  >
                    {formik.values.platformFee ? `₹${formik.values.platformFee}` : "No platform fee provided"}
                  </Box>
                )}
              </Fade>
              <FormErrorMessage fontSize={{ base: "xs", md: "sm" }}>{formik.errors.platformFee}</FormErrorMessage>
            </FormControl>
          </VStack>
        </CardBody>
      </Card>
    </ScaleFade>
  );
};

export default BasicDetailsAcademy; 
