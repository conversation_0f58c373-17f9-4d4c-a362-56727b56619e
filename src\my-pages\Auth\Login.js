import { useState } from "react";
import {
  <PERSON>lex,
  <PERSON>ing,
  Input,
  Button,
  InputGroup,
  Stack,
  InputLeftElement,
  chakra,
  Box,
  FormControl,
  InputRightElement,
  FormErrorMessage,
  useToast,
  Image,
} from "@chakra-ui/react";
import { FaUserAlt, FaLock } from "react-icons/fa";
import { useFormik } from "formik";
import * as Yup from "yup";
import axios from "axios";
import { setCookie } from "../../utilities/auth";
import { useNavigate } from "react-router-dom";
import LogoImage from "../../assets/images/mask/MainKhelCoach.svg";
import { useDispatch } from "react-redux";
import { loginSuccess, loginFailure } from "./AuthSlice";

const CFaUserAlt = chakra(FaUserAlt);
const CFaLock = chakra(FaLock);

const Login = ({ renderMe }) => {
  const [showPassword, setShowPassword] = useState(false);

  const handleShowClick = () => setShowPassword(!showPassword);

  const [isBtnLoading, setIsBtnLoading] = useState(false);

  const dispatch = useDispatch();

  const toast = useToast();
  const navigate = useNavigate();

  const validationSchema = Yup.object().shape({
    username: Yup.string()
      .min(3, "Enter atleast 3 words")
      .required("Username is required"),
    password: Yup.string().required("Password is required"),
  });

  const formik = useFormik({
    initialValues: {
      username: "",
      password: "",
    },
    validationSchema: validationSchema,
    onSubmit: (values) => {
      setIsBtnLoading(true);
      let data = JSON.stringify({
        username: `${values.username}`,
        password: `${values.password}`,
      });

      let config = {
        method: "post",
        maxBodyLength: Infinity,
        url: `${process.env.REACT_APP_BASE_URL}/api/user/login`,
        headers: {
          "Content-Type": "application/json",
        },
        data: data,
      };

      axios
        .request(config)
        .then((response) => {
          setIsBtnLoading(false);
          sessionStorage.setItem("admintoken", `Bearer ${response.data.token}`);
          setCookie("userName", response.data.user.name);
          navigate("/coach-page");
          dispatch(
            loginSuccess({
              token: `${response.data.token}`,
              user: {
                _id: response.data.user._id,
                name: response.data.user.name,
                username: response.data.user.username,
                accessScopes: response.data.user.access_scopes,
                userGroupIds: response.data.user.usergroupids,
              },
            })
          );
          renderMe();
          toast({
            title: `Welcome ${response.data.user.name}`,
            status: "success",
            duration: 3000,
            position: "top",
            isClosable: true,
          });
        })
        .catch((error) => {
          console.log(error);
          setIsBtnLoading(false);
          if (error.response.status === 400) {
            toast({
              title: error.response.data.err,
              status: "warning",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
            return;
          } else if (error.response.status === 401) {
            toast({
              title: error.response.data.err,
              status: "warning",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
            return;
          }
          toast({
            title: "Something went wrong, please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        });
    },
  });

  return (
    <>
      <Flex
        flexDirection="column"
        width="100wh"
        height="100vh"
        backgroundColor="gray.200"
        justifyContent="center"
        alignItems="center"
      >
        <Stack
          flexDir="column"
          mb="2"
          justifyContent="center"
          alignItems="center"
        >
          <Image src={LogoImage} alt="logo" w={"200px"} mb={4} />
          <form onSubmit={formik.handleSubmit}>
            <Box minW={{ base: "90%", md: "468px" }}>
              <Stack
                spacing={4}
                p="1rem"
                backgroundColor="whiteAlpha.900"
                boxShadow="md"
              >
                <FormControl
                  isInvalid={formik.errors.username && formik.touched.username}
                >
                  <InputGroup>
                    <InputLeftElement
                      pointerEvents="none"
                      children={<CFaUserAlt color="gray.300" />}
                    />
                    <Input
                      type="text"
                      placeholder="Enter username"
                      id="username"
                      name="username"
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      value={formik.values.username}
                    />
                  </InputGroup>
                  <FormErrorMessage>{formik.errors.username}</FormErrorMessage>
                </FormControl>
                <FormControl
                  isInvalid={formik.errors.password && formik.touched.password}
                >
                  <InputGroup>
                    <InputLeftElement
                      pointerEvents="none"
                      color="gray.300"
                      children={<CFaLock color="gray.300" />}
                    />
                    <Input
                      type={showPassword ? "text" : "password"}
                      placeholder="Enter password"
                      id="password"
                      name="password"
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      value={formik.values.password}
                    />
                    <InputRightElement width="4.5rem">
                      <Button h="1.75rem" size="sm" onClick={handleShowClick}>
                        {showPassword ? "Hide" : "Show"}
                      </Button>
                    </InputRightElement>
                  </InputGroup>
                  <FormErrorMessage>{formik.errors.password}</FormErrorMessage>
                </FormControl>
                <Button
                  borderRadius={0}
                  type="submit"
                  variant="solid"
                  colorScheme="teal"
                  width="full"
                  isLoading={isBtnLoading}
                >
                  Login
                </Button>
              </Stack>
            </Box>
          </form>
        </Stack>
      </Flex>
    </>
  );
};

export default Login;
