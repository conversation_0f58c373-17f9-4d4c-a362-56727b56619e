import React, { useEffect, useState } from "react";
import {
  Box,
  Button,
  Card,
  CardBody,
  Divider,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Select,
  useToast,
  FormErrorMessage,
  ButtonGroup,
  Fade,
  ScaleFade,
  useColorModeValue,
  Icon,
  VStack,
  HStack,
  Tooltip,
  Image,
  Badge,
  Grid,
  Text,
  Tag,
  TagLabel,
  TagCloseButton,
  Wrap,
  WrapItem,
  IconButton,
  Textarea,
} from "@chakra-ui/react";
import { LuCheck, LuPencilLine, LuX, LuBuilding, LuMapPin, LuHome, LuTrophy, LuImage, LuPlus, LuTrash2, LuUpload, LuBuilding2 } from "react-icons/lu";
import { useFormik } from "formik";
import axios from "axios";
import * as Yup from "yup";
import { State } from "country-state-city";

const ProfessionalDetailsAcademy = ({ academyData }) => {
  const [states, setStates] = useState([]);
  const [categories, setCategories] = useState([]);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isSubmitBtnLoading, setIsSubmitBtnLoading] = useState(false);
  const [newSportCategory, setNewSportCategory] = useState("");
  const [sportsCategories, setSportsCategories] = useState(academyData?.sportsCategories || []);
  const [academyImages, setAcademyImages] = useState(academyData?.academyImages || []);
  const [linkedFacilities, setLinkedFacilities] = useState(academyData?.linkedFacilities || []);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [showAddFacilityForm, setShowAddFacilityForm] = useState(false);
  const [pincodeError, setPincodeError] = useState(false);
  const [facilityPincodeError, setFacilityPincodeError] = useState(false);
  const [facilityErrors, setFacilityErrors] = useState({}); // Track facility-specific errors
  const [newFacility, setNewFacility] = useState({
    name: "",
    addressLine1: "",
    addressLine2: "",
    city: "",
    state: "",
    pinCode: "",
    country: "India",
    amenities: ""
  });
  const toast = useToast();
  const adminToken = sessionStorage.getItem("admintoken");
  const token = adminToken ? adminToken.split(" ")[1] : null;

  // Color mode values for better theming
  const cardBg = useColorModeValue("white", "gray.800");
  const readOnlyBg = useColorModeValue("gray.50", "gray.700");
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const textColor = useColorModeValue("gray.700", "gray.200");
  const labelColor = useColorModeValue("gray.600", "gray.300");

  useEffect(() => {
    const stateData = State.getStatesOfCountry("IN");
    setStates(stateData);
  }, []);

  // Fetch sports categories
  useEffect(() => {
    axios.get(`${process.env.REACT_APP_BASE_URL}/api/category`)
      .then(res => {
        setCategories(res.data.data || []);
      })
      .catch(err => {
        console.error("Failed to fetch categories:", err);
        toast({ 
          title: "Failed to fetch sports categories", 
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      });
  }, [toast]);

  // Function to get details from pincode for main office address
  const getDetailsFromPincode = async (pincode) => {
    try {
      if (!pincode || pincode.length !== 6) {
        setPincodeError(true);
        return;
      }
      
      setPincodeError(false);
      const details = await axios.get(
        `https://api.postalpincode.in/pincode/${pincode}`
      );
      
      if (
        details?.data[0]?.Status === "Error" ||
        details?.data[0]?.Status === "404" ||
        details?.data[0]?.Message === "No records found"
      ) {
        setPincodeError(true);
        toast({
          title: "Invalid pincode",
          description: "Please enter a valid pincode",
          status: "warning",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
        formik.setFieldError("pincode", "Pincode is not correct");
        formik.setFieldValue("city", "");
        formik.setFieldValue("state", "");
        return;
      } else {
        setPincodeError(false);
        const postOffice = details?.data[0]?.PostOffice[0];
        formik.setFieldValue("city", `${postOffice?.Name}, ${postOffice?.District}`);
        
        // Find state code from state name
        const stateData = states.find(state => state.name === postOffice?.State);
        formik.setFieldValue("state", stateData?.isoCode || "");
        formik.setFieldValue("country", "IN");
        
        toast({
          title: "Location details updated",
          description: "City, state, and country have been auto-filled",
          status: "success",
          duration: 2000,
          position: "top",
          isClosable: true,
        });
      }
    } catch (error) {
      console.log("Pincode lookup error:", error);
      setPincodeError(true);
      toast({
        title: "Error fetching location details",
        status: "error",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
    }
  };

  // Function to get details from pincode for facility
  const getFacilityDetailsFromPincode = async (pincode) => {
    try {
      if (!pincode || pincode.length !== 6) {
        setFacilityPincodeError(true);
        return;
      }
      
      setFacilityPincodeError(false);
      const details = await axios.get(
        `https://api.postalpincode.in/pincode/${pincode}`
      );
      
      if (
        details?.data[0]?.Status === "Error" ||
        details?.data[0]?.Status === "404" ||
        details?.data[0]?.Message === "No records found"
      ) {
        setFacilityPincodeError(true);
        toast({
          title: "Invalid pincode",
          description: "Please enter a valid pincode",
          status: "warning",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
        setNewFacility(prev => ({ ...prev, city: "", state: "", country: "India" }));
        return;
      } else {
        setFacilityPincodeError(false);
        const postOffice = details?.data[0]?.PostOffice[0];
        const cityName = `${postOffice?.Name}, ${postOffice?.District}`;
        
        // Find state code from state name
        const stateData = states.find(state => state.name === postOffice?.State);
        
        setNewFacility(prev => ({
          ...prev,
          city: cityName,
          state: stateData?.isoCode || "",
          country: "India"
        }));
        
        toast({
          title: "Facility location updated",
          description: "City, state, and country have been auto-filled",
          status: "success",
          duration: 2000,
          position: "top",
          isClosable: true,
        });
      }
    } catch (error) {
      console.log("Facility pincode lookup error:", error);
      setFacilityPincodeError(true);
      toast({
        title: "Error fetching location details",
        status: "error",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
    }
  };

  // Function to get details from pincode for existing facility editing
  const getExistingFacilityDetailsFromPincode = async (pincode, facilityIndex) => {
    try {
      if (!pincode || pincode.length !== 6) {
        return;
      }
      
      const details = await axios.get(
        `https://api.postalpincode.in/pincode/${pincode}`
      );
      
      if (
        details?.data[0]?.Status === "Error" ||
        details?.data[0]?.Status === "404" ||
        details?.data[0]?.Message === "No records found"
      ) {
        toast({
          title: "Invalid pincode",
          description: "Please enter a valid pincode",
          status: "warning",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
        return;
      } else {
        const postOffice = details?.data[0]?.PostOffice[0];
        const cityName = `${postOffice?.Name}, ${postOffice?.District}`;
        
        // Find state code from state name
        const stateData = states.find(state => state.name === postOffice?.State);
        
        // Update the specific facility in the linkedFacilities array
        const updatedFacilities = [...linkedFacilities];
        updatedFacilities[facilityIndex] = {
          ...updatedFacilities[facilityIndex],
          pinCode: pincode, // Explicitly preserve the correct pincode parameter
          city: cityName,
          state: stateData?.isoCode || "",
          country: "India"
        };
        setLinkedFacilities(updatedFacilities);
        
        toast({
          title: "Facility location updated",
          description: "City, state, and country have been auto-filled",
          status: "success",
          duration: 2000,
          position: "top",
          isClosable: true,
        });
      }
    } catch (error) {
      console.log("Existing facility pincode lookup error:", error);
      toast({
        title: "Error fetching location details",
        status: "error",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
    }
  };

  const validationSchema = Yup.object().shape({
    companyRegistrationNumber: Yup.string()
      .required("Company registration number is required")
      .min(3, "Must be at least 3 characters")
      .max(50, "Must be less than or equal to 50 characters"),
    officeAddress1: Yup.string().required("Office address 1 is required"),
    city: Yup.string().required("City is required"),
    state: Yup.string().required("State is required"),
    pincode: Yup.string()
      .matches(/^[0-9]{6}$/, "Invalid pincode")
      .required("Pincode is required"),
    country: Yup.string().required("Country is required"),
  });

  const formik = useFormik({
    initialValues: {
      companyRegistrationNumber: academyData?.companyRegistrationNumber || "",
      officeAddress1: academyData?.officeAddress?.addressLine1 || "",
      officeAddress2: academyData?.officeAddress?.addressLine2 || "",
      city: academyData?.officeAddress?.city || "",
      state: academyData?.officeAddress?.state || "",
      pincode: academyData?.officeAddress?.pinCode || "",
      country: academyData?.officeAddress?.country || "IN",
      sportsCategories: academyData?.sportsCategories || [],
      academyImages: academyData?.academyImages || [],
      linkedFacilities: academyData?.linkedFacilities || [],
    },
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      setIsSubmitBtnLoading(true);
      
      // Check if token exists
      if (!token) {
        toast({
          title: "Authentication error",
          description: "Please login again to continue",
          status: "error",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
        setIsSubmitBtnLoading(false);
        return;
      }

      // Check if academy ID exists
      if (!academyData?._id) {
        toast({
          title: "Academy data not found",
          description: "Please refresh the page and try again",
          status: "error",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
        setIsSubmitBtnLoading(false);
        return;
      }
      
      try {
        // Validate required fields before creating payload
        if (!values.companyRegistrationNumber?.trim()) {
          toast({
            title: "Company registration number is required",
            status: "error",
            duration: 3000,
            position: "top",
            isClosable: true,
          });
          setIsSubmitBtnLoading(false);
          return;
        }

        if (!values.officeAddress1?.trim()) {
          toast({
            title: "Office address is required",
            status: "error",
            duration: 3000,
            position: "top",
            isClosable: true,
          });
          setIsSubmitBtnLoading(false);
          return;
        }

        // Process linkedFacilities with extra validation
        const processedLinkedFacilities = linkedFacilities
          .filter(facility => facility && facility.name && facility.addressLine1) // Only include facilities with required fields
          .map(facility => {
            // Destructure to separate _id and other fields - remove all metadata
            const { _id, location, __v, createdAt, updatedAt, ...cleanFacility } = facility;
            
            // Clean up facility data and ensure no undefined/null values
            const sanitizedFacility = {
              name: (cleanFacility.name || "").trim(),
              addressLine1: (cleanFacility.addressLine1 || "").trim(),
              city: (cleanFacility.city || "").trim(),
              state: (cleanFacility.state || "").trim(),
              pinCode: (cleanFacility.pinCode || "").trim(),
              country: cleanFacility.country || "India"
            };
            
            // Add optional fields only if they have values
            if (cleanFacility.addressLine2?.trim()) {
              sanitizedFacility.addressLine2 = cleanFacility.addressLine2.trim();
            }
            if (cleanFacility.amenities?.trim()) {
              sanitizedFacility.amenities = cleanFacility.amenities.trim();
            }
            
            // Backend doesn't allow _id in the payload, so return only sanitized data
            // The backend will handle linking existing facilities by matching other fields
            return sanitizedFacility;
          })
          .filter(facility => facility.name && facility.addressLine1); // Final filter to ensure valid data

        // Build office address with only required fields first
        const officeAddress = {
          addressLine1: values.officeAddress1?.trim() || "",
          city: values.city?.trim() || "",
          state: values.state?.trim() || "",
          pinCode: values.pincode?.trim() || "",
          country: values.country === "IN" || values.country === "India" ? "IN" : values.country,
        };
        
        // Add optional office address fields only if they have values
        if (values.officeAddress2?.trim()) {
          officeAddress.addressLine2 = values.officeAddress2.trim();
        }

        const payload = {
          companyRegistrationNumber: values.companyRegistrationNumber?.trim() || "",
          officeAddress: officeAddress
        };
        
        // Add optional arrays only if they have content
        const filteredSportsCategories = sportsCategories.filter(category => category && category.trim());
        if (filteredSportsCategories.length > 0) {
          payload.sportsCategories = filteredSportsCategories;
        }
        
        const filteredAcademyImages = academyImages.filter(image => image && image.trim());
        if (filteredAcademyImages.length > 0) {
          payload.academyImages = filteredAcademyImages;
        }
        
        if (processedLinkedFacilities.length > 0) {
          payload.linkedFacilities = processedLinkedFacilities;
        }
        
        
        const response = await axios.patch(
          `${process.env.REACT_APP_BASE_URL}/api/academy/${academyData._id}`,
          payload,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );


        if (response.data.status === "success") {
          toast({
            title: "Professional details updated successfully",
            status: "success",
            duration: 3000,
            position: "top",
            isClosable: true,
          });
          setIsEditMode(false);
        } else {
          toast({
            title: response.data.message || "Something went wrong",
            status: "error",
            duration: 3000,
            position: "top",
            isClosable: true,
          });
        }
      } catch (error) {
        console.log("Professional Details API Error:", error);
        
        // More detailed error handling with field highlighting
        let errorMessage = "Something went wrong while updating professional details";
        let errorTitle = "Update Failed";
        
        if (error.response) {
          // Server responded with error status
          const responseData = error.response.data;
          
          // Handle backend validation errors with field highlighting
          if (responseData?.errors && Array.isArray(responseData.errors)) {
            errorTitle = "Validation Error";
            
            // Set field errors for highlighting
            const fieldErrors = {};
            const facilityFieldErrors = {};
            const errorMessages = [];
            
            responseData.errors.forEach(errorItem => {
              if (errorItem.field && errorItem.message) {
                // Map backend field names to formik field names
                let formikField = errorItem.field;
                
                // Handle nested field mapping for office address
                if (errorItem.field === 'officeAddress.addressLine1') {
                  formikField = 'officeAddress1';
                } else if (errorItem.field === 'officeAddress.addressLine2') {
                  formikField = 'officeAddress2';
                } else if (errorItem.field === 'officeAddress.city') {
                  formikField = 'city';
                } else if (errorItem.field === 'officeAddress.state') {
                  formikField = 'state';
                } else if (errorItem.field === 'officeAddress.pinCode') {
                  formikField = 'pincode';
                } else if (errorItem.field === 'officeAddress.country') {
                  formikField = 'country';
                }
                
                // Handle facility field errors
                if (errorItem.field.startsWith('linkedFacilities.')) {
                  // Extract facility index and field name
                  const facilityMatch = errorItem.field.match(/linkedFacilities\.(\d+)\.(.+)/);
                  if (facilityMatch) {
                    const facilityIndex = parseInt(facilityMatch[1]);
                    const facilityFieldName = facilityMatch[2];
                    
                    // Store facility errors by index and field
                    if (!facilityFieldErrors[facilityIndex]) {
                      facilityFieldErrors[facilityIndex] = {};
                    }
                    facilityFieldErrors[facilityIndex][facilityFieldName] = errorItem.message;
                    
                    errorMessages.push(`Facility ${facilityIndex + 1} - ${facilityFieldName}: ${errorItem.message}`);
                  } else {
                    errorMessages.push(`${errorItem.field}: ${errorItem.message}`);
                  }
                } else {
                  fieldErrors[formikField] = errorItem.message;
                  errorMessages.push(`${errorItem.field}: ${errorItem.message}`);
                }
              }
            });
            
            // Set field errors in formik for highlighting (only for main form fields)
            if (Object.keys(fieldErrors).length > 0) {
              formik.setErrors(fieldErrors);
              
              // Set touched fields to show errors
              const touchedFields = {};
              Object.keys(fieldErrors).forEach(field => {
                touchedFields[field] = true;
              });
              formik.setTouched(touchedFields);
            }
            
            // Set facility errors for highlighting
            if (Object.keys(facilityFieldErrors).length > 0) {
              setFacilityErrors(facilityFieldErrors);
            }
            
            errorMessage = errorMessages.join(", ");
          } else if (responseData?.details && Array.isArray(responseData.details)) {
            errorTitle = "Validation Error";
            errorMessage = responseData.details.map(detail => detail.message).join(", ");
          } else if (responseData?.message) {
            errorMessage = responseData.message;
          } else {
            errorMessage = `Server error: ${error.response.status}`;
          }
        } else if (error.request) {
          // Request was made but no response received
          errorMessage = "Network error: Unable to connect to server";
        }
        
        toast({
          title: errorTitle,
          description: errorMessage,
          status: "error",
          duration: 5000,
          position: "top",
          isClosable: true,
        });
      } finally {
        setIsSubmitBtnLoading(false);
      }
    },
  });

  const handleEdit = () => {
    setIsEditMode(true);
  };

  const handleSave = async () => {
    // Validate the form first
    const errors = await formik.validateForm();
    
    // If there are validation errors, show them and don't submit
    if (Object.keys(errors).length > 0) {
      formik.setTouched({
        companyRegistrationNumber: true,
        officeAddress1: true,
        city: true,
        state: true,
        pincode: true,
        country: true,
      });
      
      toast({
        title: "Please fix the validation errors",
        description: "Check all required fields and ensure they are filled correctly",
        status: "warning",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
      return;
    }
    
    // If validation passes, submit the form
    formik.handleSubmit();
  };

  const handleCancel = () => {
    formik.resetForm();
    setSportsCategories(academyData?.sportsCategories || []);
    setAcademyImages(academyData?.academyImages || []);
    setLinkedFacilities(academyData?.linkedFacilities || []);
    setNewSportCategory("");
    setShowAddFacilityForm(false);
    setPincodeError(false);
    setFacilityPincodeError(false);
    setFacilityErrors({}); // Clear facility errors
    setNewFacility({
      name: "",
      addressLine1: "",
      addressLine2: "",
      city: "",
      state: "",
      pinCode: "",
      country: "India",
      amenities: ""
    });
    setIsEditMode(false);
  };

  const addNewFacility = () => {
    if (newFacility.name.trim() && newFacility.addressLine1.trim() && newFacility.city.trim() && newFacility.state.trim() && newFacility.pinCode.trim()) {
      const facilityToAdd = {
        name: newFacility.name.trim(),
        addressLine1: newFacility.addressLine1.trim(),
        addressLine2: newFacility.addressLine2.trim(),
        city: newFacility.city.trim(),
        state: newFacility.state.trim(),
        pinCode: newFacility.pinCode.trim(),
        country: newFacility.country,
        amenities: newFacility.amenities.trim(), // Temporary ID for UI management only
      };
      setLinkedFacilities([...linkedFacilities, facilityToAdd]);
      setNewFacility({
        name: "",
        addressLine1: "",
        addressLine2: "",
        city: "",
        state: "",
        pinCode: "",
        country: "India",
        amenities: ""
      });
      setShowAddFacilityForm(false);
      toast({
        title: "Facility added successfully",
        description: "Don't forget to save your changes",
        status: "success",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
    } else {
      toast({
        title: "Please fill all required fields",
        description: "Name, Address Line 1, City, State, and Pin Code are required",
        status: "warning",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
    }
  };

  const cancelAddFacility = () => {
    setFacilityPincodeError(false);
    setNewFacility({
      name: "",
      addressLine1: "",
      addressLine2: "",
      city: "",
      state: "",
      pinCode: "",
      country: "India",
      amenities: ""
    });
    setShowAddFacilityForm(false);
  };

  const addSportCategory = () => {
    if (newSportCategory.trim() && !sportsCategories.includes(newSportCategory.trim())) {
      setSportsCategories([...sportsCategories, newSportCategory.trim()]);
      setNewSportCategory("");
    }
  };

  const removeSportCategory = (categoryToRemove) => {
    setSportsCategories(sportsCategories.filter(category => category !== categoryToRemove));
  };

  const handleImageUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    if (file.size > 10 * 1024 * 1024) {
      toast({
        title: "Please select a file less than 10 MB",
        status: "warning",
        duration: 5000,
        position: "top",
        isClosable: true,
      });
      return;
    }

    setUploadingImage(true);
    try {
      const formData = new FormData();
      formData.append("image", file);

      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/api/academy/uploadImage`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      const url = response?.data?.url;
      if (url) {
        setAcademyImages([...academyImages, url]);
        toast({
          title: "Image uploaded successfully",
          status: "success",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
      }
    } catch (error) {
      console.log(error);
      toast({
        title: "Error uploading image",
        status: "error",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
    } finally {
      setUploadingImage(false);
      e.target.value = "";
    }
  };

  const removeImage = (imageToRemove) => {
    setAcademyImages(academyImages.filter(image => image !== imageToRemove));
  };

  return (
    <VStack spacing={6} align="stretch">
      <ScaleFade initialScale={0.9} in={true}>
        <Card 
        bg={cardBg}
        shadow="xl"
        borderRadius="xl"
        border="1px"
        borderColor={borderColor}
        overflow="hidden"
        transition="all 0.3s ease"
        _hover={{
          shadow: "2xl",
          transform: "translateY(-2px)"
        }}
      >
        <CardBody p={{ base: 4, md: 8 }}>
          <Flex 
            justifyContent="space-between" 
            alignItems={{ base: "flex-start", md: "center" }} 
            direction={{ base: "column", md: "row" }}
            gap={{ base: 4, md: 0 }}
            mb={6}
          >
            <HStack spacing={3}>
              <Icon as={LuBuilding} boxSize={{ base: 5, md: 6 }} color="purple.500" />
              <Heading size={{ base: "md", md: "lg" }} color={textColor} fontWeight="bold">
                Professional Details
              </Heading>
            </HStack>
            <Fade in={true}>
              {!isEditMode ? (
                <Tooltip label="Edit professional information" placement="top">
                  <Button
                    colorScheme="purple"
                    variant="outline"
                    onClick={handleEdit}
                    leftIcon={<LuPencilLine />}
                    size={{ base: "sm", md: "md" }}
                    w={{ base: "full", md: "auto" }}
                    transition="all 0.2s"
                    _hover={{
                      transform: "scale(1.05)",
                      shadow: "md"
                    }}
                  >
                    Edit
                  </Button>
                </Tooltip>
              ) : (
                <ButtonGroup spacing={{ base: 2, md: 3 }} w={{ base: "full", md: "auto" }}>
                  <Button
                    colorScheme="green"
                    onClick={handleSave}
                    isLoading={isSubmitBtnLoading}
                    leftIcon={<LuCheck />}
                    size={{ base: "sm", md: "md" }}
                    flex={{ base: 1, md: "none" }}
                    transition="all 0.2s"
                    _hover={{
                      transform: "scale(1.05)",
                      shadow: "md"
                    }}
                  >
                    Save
                  </Button>
                  <Button
                    variant="outline"
                    colorScheme="red"
                    onClick={handleCancel}
                    leftIcon={<LuX />}
                    size={{ base: "sm", md: "md" }}
                    flex={{ base: 1, md: "none" }}
                    transition="all 0.2s"
                    _hover={{
                      transform: "scale(1.05)",
                      shadow: "md"
                    }}
                  >
                    Cancel
                  </Button>
                </ButtonGroup>
              )}
            </Fade>
          </Flex>
          
          <Divider borderColor={borderColor} mb={{ base: 6, md: 8 }} />
          
          <VStack spacing={{ base: 6, md: 8 }} align="stretch">
            {/* Company Registration Number Section */}
            <FormControl isInvalid={formik.errors.companyRegistrationNumber && formik.touched.companyRegistrationNumber}>
              <HStack spacing={3} mb={{ base: 3, md: 4 }}>
                <Icon as={LuBuilding} boxSize={5} color="purple.500" />
                <FormLabel fontWeight="semibold" color={labelColor} mb={0} fontSize={{ base: "sm", md: "md" }}>
                  Company Registration Number
                </FormLabel>
              </HStack>
              <Fade in={true}>
                {isEditMode ? (
                  <Input
                    type="text"
                    name="companyRegistrationNumber"
                    value={formik.values.companyRegistrationNumber}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter company registration number"
                    size={{ base: "md", md: "lg" }}
                    borderRadius="xl"
                    border="2px"
                    borderColor={
                      formik.errors.companyRegistrationNumber && formik.touched.companyRegistrationNumber 
                        ? "red.300" 
                        : borderColor
                    }
                    transition="all 0.2s"
                    _hover={{
                      borderColor: formik.errors.companyRegistrationNumber && formik.touched.companyRegistrationNumber 
                        ? "red.400" 
                        : "purple.300"
                    }}
                    _focus={{
                      borderColor: formik.errors.companyRegistrationNumber && formik.touched.companyRegistrationNumber 
                        ? "red.500" 
                        : "purple.500",
                      shadow: formik.errors.companyRegistrationNumber && formik.touched.companyRegistrationNumber 
                        ? "0 0 0 1px var(--chakra-colors-red-500)" 
                        : "0 0 0 1px var(--chakra-colors-purple-500)"
                    }}
                  />
                ) : (
                  <Box
                    p={{ base: 3, md: 4 }}
                    borderWidth="2px"
                    borderRadius="xl"
                    bg={readOnlyBg}
                    color={textColor}
                    minH={{ base: "40px", md: "48px" }}
                    display="flex"
                    alignItems="center"
                    borderColor={borderColor}
                    fontSize={{ base: "md", md: "lg" }}
                    fontWeight="medium"
                    transition="all 0.2s"
                    _hover={{
                      shadow: "sm"
                    }}
                  >
                    {formik.values.companyRegistrationNumber || "No registration number provided"}
                  </Box>
                )}
              </Fade>
              <FormErrorMessage>{formik.errors.companyRegistrationNumber}</FormErrorMessage>
            </FormControl>
            
            {/* Office Address Section */}
            <Box>
              <HStack spacing={3} mb={{ base: 4, md: 6 }}>
                <Icon as={LuMapPin} boxSize={5} color="blue.500" />
                <Heading size={{ base: "sm", md: "md" }} color={textColor} fontWeight="semibold">
                  Office Address
                </Heading>
              </HStack>
              
              <VStack spacing={{ base: 4, md: 6 }} align="stretch">
                {/* Address Line 1 */}
                <FormControl isInvalid={formik.errors.officeAddress1 && formik.touched.officeAddress1}>
                  <FormLabel fontWeight="semibold" color={labelColor} fontSize={{ base: "sm", md: "md" }}>
                    Address Line 1
                  </FormLabel>
                  <Fade in={true}>
                    {isEditMode ? (
                      <Input
                        type="text"
                        name="officeAddress1"
                        value={formik.values.officeAddress1}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="Enter address line 1"
                        size={{ base: "md", md: "lg" }}
                        borderRadius="xl"
                        border="2px"
                        borderColor={
                          formik.errors.officeAddress1 && formik.touched.officeAddress1 
                            ? "red.300" 
                            : borderColor
                        }
                        transition="all 0.2s"
                        _hover={{
                          borderColor: formik.errors.officeAddress1 && formik.touched.officeAddress1 
                            ? "red.400" 
                            : "blue.300"
                        }}
                        _focus={{
                          borderColor: formik.errors.officeAddress1 && formik.touched.officeAddress1 
                            ? "red.500" 
                            : "blue.500",
                          shadow: formik.errors.officeAddress1 && formik.touched.officeAddress1 
                            ? "0 0 0 1px var(--chakra-colors-red-500)" 
                            : "0 0 0 1px var(--chakra-colors-blue-500)"
                        }}
                      />
                    ) : (
                      <Box
                        p={{ base: 3, md: 4 }}
                        borderWidth="2px"
                        borderRadius="xl"
                        bg={readOnlyBg}
                        color={textColor}
                        minH={{ base: "40px", md: "48px" }}
                        display="flex"
                        alignItems="center"
                        borderColor={borderColor}
                        fontSize={{ base: "md", md: "lg" }}
                        fontWeight="medium"
                        transition="all 0.2s"
                        _hover={{
                          shadow: "sm"
                        }}
                      >
                        {formik.values.officeAddress1 || "No address line 1 provided"}
                      </Box>
                    )}
                  </Fade>
                  <FormErrorMessage>{formik.errors.officeAddress1}</FormErrorMessage>
                </FormControl>

                {/* Address Line 2 */}
                <FormControl isInvalid={formik.errors.officeAddress2 && formik.touched.officeAddress2}>
                  <FormLabel fontWeight="semibold" color={labelColor} fontSize={{ base: "sm", md: "md" }}>
                    Address Line 2 (Optional)
                  </FormLabel>
                  <Fade in={true}>
                    {isEditMode ? (
                      <Input
                        type="text"
                        name="officeAddress2"
                        value={formik.values.officeAddress2}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="Enter address line 2 (optional)"
                        size={{ base: "md", md: "lg" }}
                        borderRadius="xl"
                        border="2px"
                        borderColor={
                          formik.errors.officeAddress2 && formik.touched.officeAddress2 
                            ? "red.300" 
                            : borderColor
                        }
                        transition="all 0.2s"
                        _hover={{
                          borderColor: formik.errors.officeAddress2 && formik.touched.officeAddress2 
                            ? "red.400" 
                            : "blue.300"
                        }}
                        _focus={{
                          borderColor: formik.errors.officeAddress2 && formik.touched.officeAddress2 
                            ? "red.500" 
                            : "blue.500",
                          shadow: formik.errors.officeAddress2 && formik.touched.officeAddress2 
                            ? "0 0 0 1px var(--chakra-colors-red-500)" 
                            : "0 0 0 1px var(--chakra-colors-blue-500)"
                        }}
                      />
                    ) : (
                      <Box
                        p={4}
                        borderWidth="2px"
                        borderRadius="xl"
                        bg={readOnlyBg}
                        color={textColor}
                        minH="48px"
                        display="flex"
                        alignItems="center"
                        borderColor={borderColor}
                        fontSize="lg"
                        fontWeight="medium"
                        transition="all 0.2s"
                        _hover={{
                          shadow: "sm"
                        }}
                      >
                        {formik.values.officeAddress2 || "No address line 2 provided"}
                      </Box>
                    )}
                  </Fade>
                  <FormErrorMessage>{formik.errors.officeAddress2}</FormErrorMessage>
                </FormControl>
              </VStack>
            </Box>
            
            {/* Location Details Section */}
            <Box>
              <HStack spacing={3} mb={{ base: 4, md: 6 }}>
                <Icon as={LuHome} boxSize={5} color="green.500" />
                <Heading size={{ base: "sm", md: "md" }} color={textColor} fontWeight="semibold">
                  Location Details
                </Heading>
              </HStack>
              
              <VStack spacing={{ base: 4, md: 6 }} align="stretch">
                {/* City and State in a row */}
                <Flex gap={{ base: 4, md: 6 }} direction={{ base: "column", md: "row" }}>
                  <FormControl isInvalid={formik.errors.city && formik.touched.city} flex={1}>
                    <FormLabel fontWeight="semibold" color={labelColor} fontSize={{ base: "sm", md: "md" }}>
                      City
                    </FormLabel>
                    <Fade in={true}>
                      {isEditMode ? (
                        <Input
                          type="text"
                          name="city"
                          value={formik.values.city}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          placeholder="Enter city"
                          size={{ base: "md", md: "lg" }}
                          borderRadius="xl"
                          border="2px"
                          borderColor={
                            formik.errors.city && formik.touched.city 
                              ? "red.300" 
                              : borderColor
                          }
                          transition="all 0.2s"
                          _hover={{
                            borderColor: formik.errors.city && formik.touched.city 
                              ? "red.400" 
                              : "green.300"
                          }}
                          _focus={{
                            borderColor: formik.errors.city && formik.touched.city 
                              ? "red.500" 
                              : "green.500",
                            shadow: formik.errors.city && formik.touched.city 
                              ? "0 0 0 1px var(--chakra-colors-red-500)" 
                              : "0 0 0 1px var(--chakra-colors-green-500)"
                          }}
                        />
                      ) : (
                        <Box
                          p={4}
                          borderWidth="2px"
                          borderRadius="xl"
                          bg={readOnlyBg}
                          color={textColor}
                          minH="48px"
                          display="flex"
                          alignItems="center"
                          borderColor={borderColor}
                          fontSize="lg"
                          fontWeight="medium"
                          transition="all 0.2s"
                          _hover={{
                            shadow: "sm"
                          }}
                        >
                          {formik.values.city || "No city provided"}
                        </Box>
                      )}
                    </Fade>
                    <FormErrorMessage>{formik.errors.city}</FormErrorMessage>
                  </FormControl>

                  <FormControl isInvalid={formik.errors.state && formik.touched.state} flex={1}>
                    <FormLabel fontWeight="semibold" color={labelColor} fontSize={{ base: "sm", md: "md" }}>
                      State
                    </FormLabel>
                    <Fade in={true}>
                      {isEditMode ? (
                        <Select
                          name="state"
                          value={formik.values.state}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          placeholder="Select state"
                          size={{ base: "md", md: "lg" }}
                          borderRadius="xl"
                          border="2px"
                          borderColor={
                            formik.errors.state && formik.touched.state 
                              ? "red.300" 
                              : borderColor
                          }
                          transition="all 0.2s"
                          _hover={{
                            borderColor: formik.errors.state && formik.touched.state 
                              ? "red.400" 
                              : "green.300"
                          }}
                          _focus={{
                            borderColor: formik.errors.state && formik.touched.state 
                              ? "red.500" 
                              : "green.500",
                            shadow: formik.errors.state && formik.touched.state 
                              ? "0 0 0 1px var(--chakra-colors-red-500)" 
                              : "0 0 0 1px var(--chakra-colors-green-500)"
                          }}
                        >
                          {states.map((state, index) => (
                            <option key={index} value={state.isoCode}>
                              {state.name}
                            </option>
                          ))}
                        </Select>
                      ) : (
                        <Box
                          p={4}
                          borderWidth="2px"
                          borderRadius="xl"
                          bg={readOnlyBg}
                          color={textColor}
                          minH="48px"
                          display="flex"
                          alignItems="center"
                          borderColor={borderColor}
                          fontSize="lg"
                          fontWeight="medium"
                          transition="all 0.2s"
                          _hover={{
                            shadow: "sm"
                          }}
                        >
                          {states.find(state => state.isoCode === formik.values.state)?.name || "No state provided"}
                        </Box>
                      )}
                    </Fade>
                    <FormErrorMessage>{formik.errors.state}</FormErrorMessage>
                  </FormControl>
                </Flex>

                {/* Pincode and Country in a row */}
                <Flex gap={{ base: 4, md: 6 }} direction={{ base: "column", md: "row" }}>
                  <FormControl isInvalid={formik.errors.pincode && formik.touched.pincode} flex={1}>
                    <FormLabel fontWeight="semibold" color={labelColor} fontSize={{ base: "sm", md: "md" }}>
                      Pincode
                    </FormLabel>
                    <Fade in={true}>
                      {isEditMode ? (
                        <Input
                          type="text"
                          name="pincode"
                          value={formik.values.pincode}
                          onChange={(e) => {
                            formik.handleChange(e);
                            const pincode = e.target.value;
                            // Only make API call when pincode is exactly 6 digits
                            if (pincode.length === 6 && /^\d{6}$/.test(pincode)) {
                              setTimeout(() => getDetailsFromPincode(pincode), 300);
                            } else if (pincode.length < 6) {
                              setPincodeError(false);
                              formik.setFieldError("pincode", "");
                            }
                          }}
                          onBlur={formik.handleBlur}
                          placeholder="Enter pincode"
                          size="lg"
                          borderRadius="xl"
                          border="2px"
                          borderColor={pincodeError || (formik.errors.pincode && formik.touched.pincode) ? "red.300" : borderColor}
                          transition="all 0.2s"
                          _hover={{
                            borderColor: pincodeError || (formik.errors.pincode && formik.touched.pincode) ? "red.400" : "orange.300"
                          }}
                          _focus={{
                            borderColor: pincodeError || (formik.errors.pincode && formik.touched.pincode) ? "red.500" : "orange.500",
                            shadow: "0 0 0 1px var(--chakra-colors-orange-500)"
                          }}
                        />
                      ) : (
                        <Box
                          p={4}
                          borderWidth="2px"
                          borderRadius="xl"
                          bg={readOnlyBg}
                          color={textColor}
                          minH="48px"
                          display="flex"
                          alignItems="center"
                          borderColor={borderColor}
                          fontSize="lg"
                          fontWeight="medium"
                          transition="all 0.2s"
                          _hover={{
                            shadow: "sm"
                          }}
                        >
                          {formik.values.pincode || "No pincode provided"}
                        </Box>
                      )}
                    </Fade>
                    <FormErrorMessage>{formik.errors.pincode}</FormErrorMessage>
                  </FormControl>

                  <FormControl isInvalid={formik.errors.country && formik.touched.country} flex={1}>
                    <FormLabel fontWeight="semibold" color={labelColor} fontSize={{ base: "sm", md: "md" }}>
                      Country
                    </FormLabel>
                    <Fade in={true}>
                      {isEditMode ? (
                        <Select
                          name="country"
                          value={formik.values.country}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          size="lg"
                          borderRadius="xl"
                          border="2px"
                          borderColor={
                            formik.errors.country && formik.touched.country 
                              ? "red.300" 
                              : borderColor
                          }
                          transition="all 0.2s"
                          _hover={{
                            borderColor: formik.errors.country && formik.touched.country 
                              ? "red.400" 
                              : "red.300"
                          }}
                          _focus={{
                            borderColor: formik.errors.country && formik.touched.country 
                              ? "red.500" 
                              : "red.500",
                            shadow: formik.errors.country && formik.touched.country 
                              ? "0 0 0 1px var(--chakra-colors-red-500)" 
                              : "0 0 0 1px var(--chakra-colors-red-500)"
                          }}
                        >
                          <option value="IN">India</option>
                        </Select>
                      ) : (
                        <Box
                          p={4}
                          borderWidth="2px"
                          borderRadius="xl"
                          bg={readOnlyBg}
                          color={textColor}
                          minH="48px"
                          display="flex"
                          alignItems="center"
                          borderColor={borderColor}
                          fontSize="lg"
                          fontWeight="medium"
                          transition="all 0.2s"
                          _hover={{
                            shadow: "sm"
                          }}
                        >
                          {formik.values.country === "IN" ? "India" : formik.values.country || "No country provided"}
                        </Box>
                      )}
                    </Fade>
                    <FormErrorMessage>{formik.errors.country}</FormErrorMessage>
                  </FormControl>
                </Flex>
              </VStack>
            </Box>

            {/* Sports Categories Section */}
            <Box>
              <HStack spacing={3} mb={{ base: 4, md: 6 }}>
                <Icon as={LuTrophy} boxSize={5} color="gray.500" />
                <Heading size={{ base: "sm", md: "md" }} color={textColor} fontWeight="semibold">
                  Sports Categories
                </Heading>
              </HStack>
              
              <Box
                p={{ base: 3, md: 4 }}
                borderWidth="2px"
                borderRadius="xl"
                bg={readOnlyBg}
                borderColor={borderColor}
                transition="all 0.2s"
                _hover={{
                  shadow: "sm"
                }}
              >
                {isEditMode ? (
                  <VStack spacing={4} align="stretch">
                    <HStack spacing={{ base: 2, md: 3 }} flexWrap={{ base: "wrap", md: "nowrap" }}>
                      <Select
                        placeholder="Select sport category"
                        value={newSportCategory}
                        onChange={(e) => setNewSportCategory(e.target.value)}
                        size={{ base: "sm", md: "md" }}
                        borderRadius="lg"
                        flex={1}
                        minW={{ base: "full", md: "200px" }}
                      >
                        {categories.map((category) => (
                          <option key={category._id} value={category.name}>
                            {category.name}
                          </option>
                        ))}
                      </Select>
                      <Button
                        colorScheme="gray"
                        onClick={addSportCategory}
                        leftIcon={<LuPlus />}
                        size={{ base: "sm", md: "md" }}
                        w={{ base: "full", md: "auto" }}
                      >
                        Add
                      </Button>
                    </HStack>
                    {sportsCategories.length > 0 ? (
                      <Wrap spacing={2}>
                        {sportsCategories.map((sport, index) => (
                          <WrapItem key={index}>
                            <Tag
                              size="md"
                              borderRadius="full"
                              variant="solid"
                              colorScheme="gray"
                            >
                              <TagLabel>{sport}</TagLabel>
                              <TagCloseButton onClick={() => removeSportCategory(sport)} />
                            </Tag>
                          </WrapItem>
                        ))}
                      </Wrap>
                    ) : (
                      <Text color={textColor} fontSize="md" fontStyle="italic">
                        No sports categories added yet
                      </Text>
                    )}
                  </VStack>
                ) : (
                  <>
                    {sportsCategories.length > 0 ? (
                      <Flex wrap="wrap" gap={2}>
                        {sportsCategories.map((sport, index) => (
                          <Badge
                            key={index}
                            colorScheme="gray"
                            variant="subtle"
                            px={3}
                            py={1}
                            borderRadius="full"
                            fontSize="sm"
                            fontWeight="medium"
                          >
                            {sport}
                          </Badge>
                        ))}
                      </Flex>
                    ) : (
                      <Text color={textColor} fontSize="lg" fontWeight="medium">
                        No sports categories specified
                      </Text>
                    )}
                  </>
                )}
              </Box>
            </Box>

            {/* Academy Images Section */}
            <Box>
              <HStack spacing={3} mb={{ base: 4, md: 6 }}>
                <Icon as={LuImage} boxSize={5} color="pink.500" />
                <Heading size={{ base: "sm", md: "md" }} color={textColor} fontWeight="semibold">
                  Academy Images
                </Heading>
              </HStack>
              
              <Box
                p={{ base: 3, md: 4 }}
                borderWidth="2px"
                borderRadius="xl"
                bg={readOnlyBg}
                borderColor={borderColor}
                transition="all 0.2s"
                _hover={{
                  shadow: "sm"
                }}
              >
                {isEditMode && (
                  <VStack spacing={4} align="stretch">
                    <Input
                      type="file"
                      id="academyImageInput"
                      onChange={handleImageUpload}
                      style={{ display: "none" }}
                      accept="image/*"
                      multiple={false}
                    />
                    <Button
                      onClick={() => document.getElementById('academyImageInput').click()}
                      colorScheme="pink"
                      variant="outline"
                      leftIcon={<LuUpload />}
                      isLoading={uploadingImage}
                      loadingText="Uploading..."
                      size={{ base: "sm", md: "md" }}
                      w={{ base: "full", md: "auto" }}
                    >
                      Upload New Image
                    </Button>
                  </VStack>
                )}

                {academyImages.length > 0 ? (
                  <Grid 
                    templateColumns={{ 
                      base: "repeat(auto-fill, minmax(60px, 1fr))", 
                      md: "repeat(auto-fill, minmax(80px, 1fr))" 
                    }}
                    gap={{ base: 2, md: 3 }}
                    w="full"
                    mt={isEditMode ? 4 : 0}
                  >
                    {academyImages.map((imageUrl, index) => (
                      <Box key={index} position="relative">
                        <Image
                          src={imageUrl}
                          alt={`Academy image ${index + 1}`}
                          boxSize={{ base: "60px", md: "80px" }}
                          objectFit="cover"
                          borderRadius="lg"
                          border="2px"
                          borderColor="pink.200"
                          transition="all 0.2s"
                          _hover={{
                            borderColor: "pink.400",
                            transform: "scale(1.1)"
                          }}
                          cursor="pointer"
                          onClick={() => window.open(imageUrl, '_blank')}
                        />
                        {isEditMode && (
                          <IconButton
                            icon={<LuTrash2 />}
                            size="xs"
                            colorScheme="red"
                            variant="solid"
                            position="absolute"
                            top="-5px"
                            right="-5px"
                            borderRadius="full"
                            onClick={(e) => {
                              e.stopPropagation();
                              removeImage(imageUrl);
                            }}
                            aria-label="Remove image"
                          />
                        )}
                      </Box>
                    ))}
                  </Grid>
                ) : (
                  <Text 
                    color={textColor} 
                    fontSize="lg" 
                    fontWeight="medium"
                    mt={isEditMode ? 4 : 0}
                  >
                    No academy images uploaded
                  </Text>
                )}
              </Box>
            </Box>
          </VStack>
        </CardBody>
      </Card>

      {/* Linked Facilities Section - Separate Card */}
      <Card 
        bg={cardBg}
        shadow="xl"
        borderRadius="xl"
        border="1px"
        borderColor={borderColor}
        overflow="hidden"
        transition="all 0.3s ease"
        _hover={{
          shadow: "2xl",
          transform: "translateY(-2px)"
        }}
        mt={6}
      >
        <CardBody p={{ base: 4, md: 8 }}>
          <Flex 
            justifyContent="space-between" 
            alignItems={{ base: "flex-start", md: "center" }} 
            direction={{ base: "column", md: "row" }}
            gap={{ base: 4, md: 0 }}
            mb={6}
          >
            <HStack spacing={3}>
              <Icon as={LuBuilding2} boxSize={{ base: 5, md: 6 }} color="blue.500" />
              <Heading size={{ base: "md", md: "lg" }} color={textColor} fontWeight="bold">
                Facility
              </Heading>
            </HStack>
          </Flex>
          
          <Divider borderColor={borderColor} mb={{ base: 6, md: 8 }} />
          
          {/* Add New Facility Button in Edit Mode */}
          {isEditMode && (
            <Box mb={{ base: 4, md: 6 }}>
              {!showAddFacilityForm ? (
                <Button
                  colorScheme="blue"
                  variant="outline"
                  leftIcon={<LuPlus />}
                  size={{ base: "sm", md: "md" }}
                  borderStyle="dashed"
                  borderWidth="2px"
                  py={{ base: 2, md: 3 }}
                  fontSize={{ base: "xs", md: "sm" }}
                  fontWeight="semibold"
                  w={{ base: "full", md: "auto" }}
                  transition="all 0.3s ease"
                  _hover={{
                    borderColor: "blue.400",
                    bg: "blue.50",
                    transform: "translateY(-1px)",
                    shadow: "md"
                  }}
                  onClick={() => setShowAddFacilityForm(true)}
                >
                  Add New Facility
                </Button>
              ) : (
                <Card
                  bg={cardBg}
                  shadow="md"
                  borderRadius="xl"
                  border="2px"
                  borderColor="blue.200"
                  borderStyle="dashed"
                >
                  <CardBody p={{ base: 4, md: 6 }}>
                    <VStack spacing={{ base: 3, md: 4 }} align="stretch">
                      <HStack justify="space-between" align="center">
                        <Heading size={{ base: "xs", md: "sm" }} color="blue.600" fontWeight="bold">
                          Add New Facility
                        </Heading>
                        <IconButton
                          icon={<LuX />}
                          size="sm"
                          variant="ghost"
                          colorScheme="red"
                          onClick={cancelAddFacility}
                          aria-label="Cancel adding facility"
                        />
                      </HStack>
                      
                      <Divider borderColor="blue.200" />
                      
                      <VStack spacing={4} align="stretch">
                        {/* Facility Name */}
                        <FormControl isRequired>
                          <FormLabel fontSize="sm" fontWeight="semibold" color={labelColor}>
                            Facility Name
                          </FormLabel>
                          <Input
                            value={newFacility.name}
                            onChange={(e) => setNewFacility({ ...newFacility, name: e.target.value })}
                            placeholder="Enter facility name"
                            size="sm"
                            borderRadius="lg"
                          />
                        </FormControl>

                        {/* Address Lines */}
                        <FormControl isRequired>
                          <FormLabel fontSize="sm" fontWeight="semibold" color={labelColor}>
                            Address Line 1
                          </FormLabel>
                          <Input
                            value={newFacility.addressLine1}
                            onChange={(e) => setNewFacility({ ...newFacility, addressLine1: e.target.value })}
                            placeholder="Enter address line 1"
                            size="sm"
                            borderRadius="lg"
                          />
                        </FormControl>

                        <FormControl>
                          <FormLabel fontSize="sm" fontWeight="semibold" color={labelColor}>
                            Address Line 2 (Optional)
                          </FormLabel>
                          <Input
                            value={newFacility.addressLine2}
                            onChange={(e) => setNewFacility({ ...newFacility, addressLine2: e.target.value })}
                            placeholder="Enter address line 2 (optional)"
                            size="sm"
                            borderRadius="lg"
                          />
                        </FormControl>

                        {/* Location Details */}
                        <Flex gap={3} direction={{ base: "column", md: "row" }}>
                          <FormControl isRequired flex={1}>
                            <FormLabel fontSize="sm" fontWeight="semibold" color={labelColor}>
                              City
                            </FormLabel>
                            <Input
                              value={newFacility.city}
                              onChange={(e) => setNewFacility({ ...newFacility, city: e.target.value })}
                              placeholder="Enter city"
                              size="sm"
                              borderRadius="lg"
                            />
                          </FormControl>

                          <FormControl isRequired flex={1}>
                            <FormLabel fontSize="sm" fontWeight="semibold" color={labelColor}>
                              State
                            </FormLabel>
                            <Select
                              value={newFacility.state}
                              onChange={(e) => setNewFacility({ ...newFacility, state: e.target.value })}
                              placeholder="Select state"
                              size="sm"
                              borderRadius="lg"
                            >
                              {states.map((state, index) => (
                                <option key={index} value={state.isoCode}>
                                  {state.name}
                                </option>
                              ))}
                            </Select>
                          </FormControl>
                        </Flex>

                        <Flex gap={3} direction={{ base: "column", md: "row" }}>
                          <FormControl isRequired flex={1}>
                            <FormLabel fontSize="sm" fontWeight="semibold" color={labelColor}>
                              Pin Code
                            </FormLabel>
                            <Input
                              value={newFacility.pinCode}
                              onChange={(e) => {
                                const pincode = e.target.value;
                                setNewFacility(prev => ({ ...prev, pinCode: pincode }));
                                // Only make API call when pincode is exactly 6 digits
                                if (pincode.length === 6 && /^\d{6}$/.test(pincode)) {
                                  setTimeout(() => getFacilityDetailsFromPincode(pincode), 300);
                                } else if (pincode.length < 6) {
                                  setFacilityPincodeError(false);
                                }
                              }}
                              placeholder="Enter pin code"
                              size="sm"
                              borderRadius="lg"
                              borderColor={facilityPincodeError ? "red.300" : "gray.200"}
                              _hover={{
                                borderColor: facilityPincodeError ? "red.400" : "gray.300"
                              }}
                              _focus={{
                                borderColor: facilityPincodeError ? "red.500" : "blue.500"
                              }}
                            />
                          </FormControl>

                          <FormControl flex={1}>
                            <FormLabel fontSize="sm" fontWeight="semibold" color={labelColor}>
                              Country
                            </FormLabel>
                            <Select
                              value={newFacility.country}
                              onChange={(e) => setNewFacility({ ...newFacility, country: e.target.value })}
                              size="sm"
                              borderRadius="lg"
                            >
                              <option value="India">India</option>
                            </Select>
                          </FormControl>
                        </Flex>

                        {/* Amenities */}
                        <FormControl>
                          <FormLabel fontSize="sm" fontWeight="semibold" color={labelColor}>
                            Amenities (Optional)
                          </FormLabel>
                          <Textarea
                            value={newFacility.amenities}
                            onChange={(e) => setNewFacility({ ...newFacility, amenities: e.target.value })}
                            placeholder="Describe available amenities..."
                            size="sm"
                            borderRadius="lg"
                            rows={3}
                          />
                        </FormControl>

                        {/* Action Buttons */}
                        <HStack spacing={3} pt={2}>
                          <Button
                            colorScheme="blue"
                            size="sm"
                            leftIcon={<LuCheck />}
                            onClick={addNewFacility}
                            flex={1}
                          >
                            Add Facility
                          </Button>
                          <Button
                            variant="outline"
                            colorScheme="red"
                            size="sm"
                            leftIcon={<LuX />}
                            onClick={cancelAddFacility}
                            flex={1}
                          >
                            Cancel
                          </Button>
                        </HStack>
                      </VStack>
                    </VStack>
                  </CardBody>
                </Card>
              )}
            </Box>
          )}
          
          {linkedFacilities.length > 0 ? (
            <VStack spacing={{ base: 3, md: 4 }} align="stretch">
              {linkedFacilities.map((facility, index) => (
                <Card
                  key={index}
                  bg={cardBg}
                  shadow="md"
                  borderRadius="xl"
                  border="1px"
                  borderColor={borderColor}
                  overflow="hidden"
                  transition="all 0.3s ease"
                  _hover={{
                    shadow: "lg",
                    transform: "translateY(-1px)"
                  }}
                >
                  <CardBody p={{ base: 4, md: 6 }}>
                    <VStack spacing={{ base: 3, md: 4 }} align="stretch">
                      <HStack justify="space-between" align="center" flexWrap="wrap">
                        <HStack spacing={3} flex={1} minW="200px">
                          <Icon as={LuBuilding2} boxSize={5} color="blue.500" />
                          {isEditMode ? (
                            <Input
                              value={facility.name}
                              onChange={(e) => {
                                const updatedFacilities = [...linkedFacilities];
                                updatedFacilities[index] = { ...facility, name: e.target.value };
                                setLinkedFacilities(updatedFacilities);
                              }}
                              placeholder="Facility name"
                              size="sm"
                              fontWeight="bold"
                              color="blue.600"
                              borderColor={
                                facilityErrors[index]?.name 
                                  ? "red.300" 
                                  : "gray.200"
                              }
                              _hover={{
                                borderColor: facilityErrors[index]?.name 
                                  ? "red.400" 
                                  : "blue.300"
                              }}
                              _focus={{
                                borderColor: facilityErrors[index]?.name 
                                  ? "red.500" 
                                  : "blue.500",
                                shadow: facilityErrors[index]?.name 
                                  ? "0 0 0 1px var(--chakra-colors-red-500)" 
                                  : "0 0 0 1px var(--chakra-colors-blue-500)"
                              }}
                            />
                          ) : (
                            <Heading 
                              size={{ base: "sm", md: "md" }} 
                              color="blue.600" 
                              fontWeight="bold"
                              wordBreak="break-word"
                            >
                              {facility.name}
                            </Heading>
                          )}
                        </HStack>
                        {isEditMode && (
                          <HStack spacing={2}>
                            <IconButton
                              icon={<LuTrash2 />}
                              size="sm"
                              colorScheme="red"
                              variant="outline"
                              onClick={() => {
                                setLinkedFacilities(linkedFacilities.filter((_, i) => i !== index));
                              }}
                              aria-label="Remove facility"
                            />
                          </HStack>
                        )}
                      </HStack>
                      
                      <Divider borderColor={borderColor} />
                      
                      {isEditMode ? (
                        // Edit mode for facility - all facilities are editable
                        <VStack spacing={4} align="stretch">
                          <FormControl isRequired>
                            <FormLabel fontSize="sm" fontWeight="semibold" color={labelColor}>
                              Address Line 1
                            </FormLabel>
                            <Input
                              value={facility.addressLine1}
                              onChange={(e) => {
                                const updatedFacilities = [...linkedFacilities];
                                updatedFacilities[index] = { ...facility, addressLine1: e.target.value };
                                setLinkedFacilities(updatedFacilities);
                              }}
                              placeholder="Enter address line 1"
                              size="sm"
                              borderColor={
                                facilityErrors[index]?.addressLine1 
                                  ? "red.300" 
                                  : "gray.200"
                              }
                              _hover={{
                                borderColor: facilityErrors[index]?.addressLine1 
                                  ? "red.400" 
                                  : "blue.300"
                              }}
                              _focus={{
                                borderColor: facilityErrors[index]?.addressLine1 
                                  ? "red.500" 
                                  : "blue.500",
                                shadow: facilityErrors[index]?.addressLine1 
                                  ? "0 0 0 1px var(--chakra-colors-red-500)" 
                                  : "0 0 0 1px var(--chakra-colors-blue-500)"
                              }}
                            />
                          </FormControl>

                          <FormControl>
                            <FormLabel fontSize="sm" fontWeight="semibold" color={labelColor}>
                              Address Line 2 (Optional)
                            </FormLabel>
                            <Input
                              value={facility.addressLine2 || ""}
                              onChange={(e) => {
                                const updatedFacilities = [...linkedFacilities];
                                updatedFacilities[index] = { ...facility, addressLine2: e.target.value };
                                setLinkedFacilities(updatedFacilities);
                              }}
                              placeholder="Enter address line 2 (optional)"
                              size="sm"
                              borderColor={
                                facilityErrors[index]?.addressLine2 
                                  ? "red.300" 
                                  : "gray.200"
                              }
                              _hover={{
                                borderColor: facilityErrors[index]?.addressLine2 
                                  ? "red.400" 
                                  : "blue.300"
                              }}
                              _focus={{
                                borderColor: facilityErrors[index]?.addressLine2 
                                  ? "red.500" 
                                  : "blue.500",
                                shadow: facilityErrors[index]?.addressLine2 
                                  ? "0 0 0 1px var(--chakra-colors-red-500)" 
                                  : "0 0 0 1px var(--chakra-colors-blue-500)"
                              }}
                            />
                          </FormControl>

                          <Flex gap={3} direction={{ base: "column", md: "row" }}>
                            <FormControl isRequired flex={1}>
                              <FormLabel fontSize="sm" fontWeight="semibold" color={labelColor}>
                                City
                              </FormLabel>
                              <Input
                                value={facility.city}
                                onChange={(e) => {
                                  const updatedFacilities = [...linkedFacilities];
                                  updatedFacilities[index] = { ...facility, city: e.target.value };
                                  setLinkedFacilities(updatedFacilities);
                                }}
                                placeholder="Enter city"
                                size="sm"
                              />
                            </FormControl>

                            <FormControl isRequired flex={1}>
                              <FormLabel fontSize="sm" fontWeight="semibold" color={labelColor}>
                                State
                              </FormLabel>
                              <Select
                                value={facility.state}
                                onChange={(e) => {
                                  const updatedFacilities = [...linkedFacilities];
                                  updatedFacilities[index] = { ...facility, state: e.target.value };
                                  setLinkedFacilities(updatedFacilities);
                                }}
                                placeholder="Select state"
                                size="sm"
                              >
                                {states.map((state, stateIndex) => (
                                  <option key={stateIndex} value={state.isoCode}>
                                    {state.name}
                                  </option>
                                ))}
                              </Select>
                            </FormControl>
                          </Flex>

                          <Flex gap={3} direction={{ base: "column", md: "row" }}>
                            <FormControl isRequired flex={1}>
                              <FormLabel fontSize="sm" fontWeight="semibold" color={labelColor}>
                                Pin Code
                              </FormLabel>
                              <Input
                                value={facility.pinCode}
                                onChange={(e) => {
                                  const pincode = e.target.value;
                                  const updatedFacilities = [...linkedFacilities];
                                  updatedFacilities[index] = { ...facility, pinCode: pincode };
                                  setLinkedFacilities(updatedFacilities);
                                  
                                  // Only make API call when pincode is exactly 6 digits
                                  if (pincode.length === 6 && /^\d{6}$/.test(pincode)) {
                                    setTimeout(() => getExistingFacilityDetailsFromPincode(pincode, index), 300);
                                  }
                                }}
                                placeholder="Enter pin code"
                                size="sm"
                              />
                            </FormControl>

                            <FormControl flex={1}>
                              <FormLabel fontSize="sm" fontWeight="semibold" color={labelColor}>
                                Country
                              </FormLabel>
                              <Select
                                value={facility.country || "India"}
                                onChange={(e) => {
                                  const updatedFacilities = [...linkedFacilities];
                                  updatedFacilities[index] = { ...facility, country: e.target.value };
                                  setLinkedFacilities(updatedFacilities);
                                }}
                                size="sm"
                              >
                                <option value="India">India</option>
                              </Select>
                            </FormControl>
                          </Flex>

                          <FormControl>
                            <FormLabel fontSize="sm" fontWeight="semibold" color={labelColor}>
                              Amenities (Optional)
                            </FormLabel>
                            <Textarea
                              value={facility.amenities || ""}
                              onChange={(e) => {
                                const updatedFacilities = [...linkedFacilities];
                                updatedFacilities[index] = { ...facility, amenities: e.target.value };
                                setLinkedFacilities(updatedFacilities);
                              }}
                              placeholder="Describe available amenities..."
                              size="sm"
                              rows={3}
                              borderColor={
                                facilityErrors[index]?.amenities 
                                  ? "red.300" 
                                  : "gray.200"
                              }
                              _hover={{
                                borderColor: facilityErrors[index]?.amenities 
                                  ? "red.400" 
                                  : "blue.300"
                              }}
                              _focus={{
                                borderColor: facilityErrors[index]?.amenities 
                                  ? "red.500" 
                                  : "blue.500",
                                shadow: facilityErrors[index]?.amenities 
                                  ? "0 0 0 1px var(--chakra-colors-red-500)" 
                                  : "0 0 0 1px var(--chakra-colors-blue-500)"
                              }}
                            />
                            {facilityErrors[index]?.amenities && (
                              <Text color="red.500" fontSize="sm" mt={1}>
                                {facilityErrors[index].amenities}
                              </Text>
                            )}
                          </FormControl>
                        </VStack>
                      ) : (
                        // View mode for facility
                        <VStack spacing={{ base: 2, md: 3 }} align="stretch">
                          <Box>
                            <Text fontSize={{ base: "xs", md: "sm" }} fontWeight="semibold" color={labelColor} mb={1}>
                              Address:
                            </Text>
                            <Text fontSize={{ base: "sm", md: "md" }} color={textColor} wordBreak="break-word">
                              {facility.addressLine1}
                              {facility.addressLine2 && `, ${facility.addressLine2}`}
                            </Text>
                          </Box>
                          
                          <Box>
                            <Text fontSize={{ base: "xs", md: "sm" }} fontWeight="semibold" color={labelColor} mb={1}>
                              Location:
                            </Text>
                            <Text fontSize={{ base: "sm", md: "md" }} color={textColor} wordBreak="break-word">
                              {facility.city}, {facility.state} - {facility.pinCode}, {facility.country}
                            </Text>
                          </Box>
                          
                          {facility.amenities && (
                            <Box>
                              <Text fontSize={{ base: "xs", md: "sm" }} fontWeight="semibold" color={labelColor} mb={2}>
                                Amenities:
                              </Text>
                              <Box 
                                p={{ base: 2, md: 3 }}
                                borderRadius="lg"
                                bg={readOnlyBg}
                                borderWidth="1px"
                                borderColor={borderColor}
                                dangerouslySetInnerHTML={{ __html: facility.amenities }}
                                sx={{
                                  "& ol": { paddingLeft: "20px", color: textColor },
                                  "& li": { marginBottom: "4px", color: textColor, fontSize: { base: "xs", md: "sm" } },
                                  "& ul": { paddingLeft: "20px", color: textColor }
                                }}
                              />
                            </Box>
                          )}
                        </VStack>
                      )}
                    </VStack>
                  </CardBody>
                </Card>
              ))}
            </VStack>
          ) : (
            <Card
              bg={readOnlyBg}
              shadow="sm"
              borderRadius="xl"
              border="2px"
              borderColor={borderColor}
              borderStyle="dashed"
            >
              <CardBody p={{ base: 6, md: 8 }}>
                <VStack spacing={{ base: 4, md: 6 }}>
                  <Icon as={LuBuilding2} boxSize={{ base: 12, md: 16 }} color="gray.400" />
                  <Text color={textColor} fontSize={{ base: "lg", md: "xl" }} fontWeight="medium" textAlign="center">
                    No linked facilities available
                  </Text>
                  {isEditMode ? (
                    <VStack spacing={4}>
                      <Text color={labelColor} fontSize={{ base: "sm", md: "md" }} textAlign="center" maxW="md">
                        Click the button above to add your first facility
                      </Text>
                    </VStack>
                  ) : (
                    <Text color={labelColor} fontSize={{ base: "sm", md: "md" }} textAlign="center" maxW="md">
                      Contact your administrator to link facilities to your academy.
                    </Text>
                  )}
                </VStack>
              </CardBody>
            </Card>
          )}
        </CardBody>
      </Card>
    </ScaleFade>
    </VStack>
  );
};

export default ProfessionalDetailsAcademy; 
