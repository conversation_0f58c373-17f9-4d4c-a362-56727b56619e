import React, { useState, useEffect } from "react";
import {
  Box,
  Button,
  Card,
  CardBody,
  FormLabel,
  Heading,
  Image,
  ScaleFade,
  useColorModeValue,
  Icon,
  VStack,
  HStack,
  Badge,
  Text,
  Flex,
  Input,
  Checkbox,
  FormControl,
  FormErrorMessage,
  useToast,
} from "@chakra-ui/react";
import { LuShield, LuCreditCard, LuFileText, LuBadgeCheck, LuPencilLine, LuBanknote, LuCheck, LuX } from "react-icons/lu";
import { useFormik } from "formik";
import * as Yup from "yup";
import axios from "axios";
import { useParams } from "react-router-dom";
import { useSelector } from "react-redux";

const KYCDetailsAcademy = ({ academyData }) => {
  const [isLoadingSubmitBtn, setIsLoadingSubmitBtn] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const toast = useToast();
  const { id } = useParams();
  const token = sessionStorage.getItem("admintoken")?.split(" ")[1];
  const userData = useSelector((state) => state.user);

  // Color mode values for better theming
  const cardBg = useColorModeValue("white", "gray.800");
  const inputBg = useColorModeValue("white", "gray.700");
  const readOnlyBg = useColorModeValue("gray.50", "gray.700");
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const textColor = useColorModeValue("gray.700", "gray.200");
  const labelColor = useColorModeValue("gray.600", "gray.300");

  const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;

  const formik = useFormik({

    enableReinitialize: true,
    initialValues: {
      panNumber: academyData?.panNumber || "",
      panImage: Array.isArray(academyData?.panImage) ? academyData?.panImage : (academyData?.panImage ? [academyData?.panImage] : []),
      aadhaarNumber: academyData?.aadhaarNumber || "",
      aadhaarImage: Array.isArray(academyData?.aadhaarImage) ? academyData?.aadhaarImage : (academyData?.aadhaarImage ? [academyData?.aadhaarImage] : []),
      hasGst: Boolean(academyData?.gstNumber), // Derive from gstNumber existence
      gstNumber: academyData?.gstNumber || "",
      accountHolderName: academyData?.bankDetails?.accountHolderName || "",
      accountNumber: academyData?.bankDetails?.accountNumber || "",
      ifsc: academyData?.bankDetails?.ifsc || "",
    },
    validationSchema: Yup.object().shape({
      panNumber: Yup.string()
        .required("PAN number is required")
        .matches(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, "Invalid PAN number"),
      panImage: Yup.array()
        .min(1, "At least one PAN image is required"),
      aadhaarNumber: Yup.string()
        .required("Aadhaar number is required")
        .matches(/^[0-9]{12}$/, "Invalid Aadhaar number"),
      aadhaarImage: Yup.array()
        .min(2, "Both front and back Aadhaar images are required")
        .max(2, "Only 2 Aadhaar images are allowed"),
      hasGst: Yup.boolean(),
      gstNumber: Yup.string()
        .test('gst-validation', 'Please enter a valid GST number', function(value) {
          const { hasGst } = this.parent;
          if (hasGst && (!value || !gstRegex.test(value))) {
            return false;
          }
          if (!hasGst && value && !gstRegex.test(value)) {
            return false;
          }
          return true;
        }),
      accountHolderName: Yup.string().required("Account holder name is required"),
      accountNumber: Yup.string().required("Account number is required"),
      ifsc: Yup.string().required("IFSC code is required"),
    }),
    onSubmit: async (values) => {
      setIsLoadingSubmitBtn(true);
      try {
        // Validate token exists
        if (!token) {
          toast({
            title: "Authentication token not found. Please login again.",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
          return;
        }

        // Validate academy ID exists
        if (!academyData?._id) {
          toast({
            title: "Academy ID not found. Please refresh the page.",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
          return;
        }

        // Prepare payload without hasGst field - backend determines GST status from gstNumber presence
        const payload = {
          panNumber: values.panNumber,
          panImage: values.panImage,
          aadhaarNumber: values.aadhaarNumber,
          aadhaarImage: values.aadhaarImage,
          bankDetails: {
            accountHolderName: values.accountHolderName,
            accountNumber: values.accountNumber,
            ifsc: values.ifsc,
          }
        };

        // Only include gstNumber if GST is enabled and gstNumber is provided
        if (values.hasGst && values.gstNumber) {
          payload.gstNumber = values.gstNumber;
        }


        const response = await axios.patch(
          `${process.env.REACT_APP_BASE_URL}/api/academy/${academyData._id}`,
          payload,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );


        if (response.data.status === "success") {
          toast({
            title: "KYC details updated successfully",
            status: "success",
            duration: 3000,
            position: "top",
            isClosable: true,
          });
          setIsEditing(false); // Exit edit mode after successful save
        } else {
          toast({
            title: "Failed to update KYC details",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      } catch (error) {
        console.error("KYC Update Error:", error);
        console.error("Error Response:", error.response?.data);
        
        if (error.response?.status === 403) {
          toast({
            title: "You don't have access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else if (error.response?.status === 401) {
          toast({
            title: "Authentication failed. Please login again.",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else if (error.response?.status === 400) {
          toast({
            title: error.response?.data?.message || "Invalid data provided",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else if (error.response?.status === 404) {
          toast({
            title: "Academy not found",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: error.response?.data?.message || "Something went wrong while updating KYC details",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      } finally {
        setIsLoadingSubmitBtn(false);
      }
    },
  });

  const handleEditClick = () => {
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    formik.resetForm();
  };

  const handleFileChange = async (e, fieldName, index = null) => {
    try {
      const file = e.currentTarget.files[0];

      if (file && file.size > 10 * 1024 * 1024) {
        toast({
          title: "Please select a file less than 10 MB.",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
        return;
      }

      const formData = new FormData();
      formData.append("image", file);

      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/api/academy/uploadImage`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      const url = response?.data?.url;
      if (url) {
        if (index !== null) {
          // Handle array fields (aadhaarImage, panImage)
          const currentArray = [...formik.values[fieldName]];
          if (fieldName === "panImage") {
            // For PAN images, add to array instead of replacing at index
            currentArray.push(url);
          } else {
            // For Aadhaar images, replace at specific index
            currentArray[index] = url;
          }
          formik.setFieldValue(fieldName, currentArray);
        } else {
          // Handle single field (legacy support)
          formik.setFieldValue(fieldName, url);
        }

        toast({
          title: "Image Uploaded Successfully",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title: "Something went wrong while uploading image, please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
    } catch (error) {
      console.log(error);
      if (error.response?.status === 403) {
        toast({
          title: "You don't have an access to perform this action",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
    }
  };

  const deleteImageFiles = async (fieldName, index = null) => {
    if (!userData?.accessScopes?.academy?.includes("delete")) {
      toast({
        title: "You don't have an access to perform this action",
        status: "warning",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    } else {
      try {
        let url;
        if (index !== null) {
          // Handle array fields
          url = formik.values[fieldName][index];
        } else {
          // Handle single field (legacy support)
          url = formik.values[fieldName];
        }

        const formData = new FormData();
        formData.append("url", url);

        await axios.post(
          `${process.env.REACT_APP_BASE_URL}/api/academy/uploadImage`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );

        if (index !== null) {
          // Handle array fields
          const currentArray = [...formik.values[fieldName]];
          currentArray[index] = "";
          formik.setFieldValue(fieldName, currentArray);
        } else {
          // Handle single field (legacy support)
          formik.setFieldValue(fieldName, "");
        }

        toast({
          title: "Image removed Successfully",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } catch (error) {
        console.log(error);
        if (error.response?.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      }
    }
  };

  return (
    <ScaleFade initialScale={0.9} in={true}>
      <VStack spacing={{ base: 4, md: 6 }} align="stretch" w="full" maxW="100%">
        {/* Header Section */}
        <Card 
          bg={cardBg}
          shadow="md"
          borderRadius={{ base: "lg", md: "xl" }}
          border="1px"
          borderColor={borderColor}
          w="full"
        >
          <CardBody p={{ base: 4, md: 6 }}>
            <Flex 
              justifyContent="space-between" 
              alignItems={{ base: "flex-start", md: "center" }}
              direction={{ base: "column", md: "row" }}
              gap={{ base: 4, md: 0 }}
            >
              <HStack spacing={3}>
                <Icon as={LuShield} boxSize={6} color="blue.500" />
                <Heading size={{ base: "md", md: "lg" }} color={textColor} fontWeight="bold">
                  KYC Details
                </Heading>
              </HStack>
              {!isEditing ? (
                <Button
                  colorScheme="blue"
                  variant="outline"
                  leftIcon={<LuPencilLine />}
                  size={{ base: "sm", md: "md" }}
                  borderRadius="md"
                  onClick={handleEditClick}
                  w={{ base: "full", md: "auto" }}
                >
                  Edit
                </Button>
              ) : (
                <HStack spacing={3} w={{ base: "full", md: "auto" }}>
                  <Button
                    colorScheme="green"
                    variant="solid"
                    leftIcon={<LuCheck />}
                    size={{ base: "sm", md: "md" }}
                    borderRadius="md"
                    onClick={formik.handleSubmit}
                    isLoading={isLoadingSubmitBtn}
                    loadingText="Saving..."
                    flex={{ base: 1, md: "none" }}
                    type="submit"
                  >
                    Save
                  </Button>
                  <Button
                    colorScheme="red"
                    variant="outline"
                    size={{ base: "sm", md: "md" }}
                    borderRadius="md"
                    onClick={handleCancelEdit}
                    leftIcon={<LuX />}
                    flex={{ base: 1, md: "none" }}
                  >
                    Cancel
                  </Button>
                </HStack>
              )}
            </Flex>
          </CardBody>
        </Card>

        {/* GST Details Section */}
        <Card 
          bg={cardBg}
          shadow="xl"
          borderRadius={{ base: "lg", md: "xl" }}
          border="1px"
          borderColor="orange.200"
          overflow="hidden"
          transition="all 0.3s ease"
          w="full"
          _hover={{
            shadow: "2xl",
            transform: "translateY(-2px)",
            borderColor: "orange.300"
          }}
        >
          <CardBody p={{ base: 4, md: 6 }}>
            <HStack spacing={3} mb={{ base: 4, md: 6 }}>
              <Icon as={LuBadgeCheck} boxSize={5} color="orange.500" />
              <Heading size={{ base: "sm", md: "md" }} color={textColor} fontWeight="semibold">
                GST Registration
              </Heading>
            </HStack>
            
            <VStack spacing={{ base: 4, md: 6 }} align="stretch">
              {/* GST Registration Status */}
              <FormControl>
                <FormLabel fontWeight="semibold" color={labelColor} mb={3}>
                  GST Registration Status
                </FormLabel>
                {isEditing ? (
                  <Checkbox
                    isChecked={formik.values.hasGst}
                    onChange={(e) => formik.setFieldValue("hasGst", e.target.checked)}
                    colorScheme="orange"
                    size="lg"
                  >
                    <Text fontSize="lg" fontWeight="medium" color={textColor}>
                      GST Registered
                    </Text>
                  </Checkbox>
                ) : (
                  <Box
                    p={4}
                    borderWidth="2px"
                    borderRadius="xl"
                    bg={readOnlyBg}
                    color={textColor}
                    minH="48px"
                    display="flex"
                    alignItems="center"
                    borderColor={borderColor}
                    fontSize="lg"
                    fontWeight="medium"
                    transition="all 0.2s"
                    _hover={{
                      shadow: "sm"
                    }}
                  >
                    <HStack spacing={3}>
                      <Icon 
                        as={LuBadgeCheck} 
                        color={formik.values.hasGst ? "green.500" : "gray.400"} 
                        boxSize={5}
                      />
                      <Text>
                        {formik.values.hasGst ? "GST Registered" : "Not GST Registered"}
                      </Text>
                      {formik.values.hasGst && (
                        <Badge colorScheme="green" variant="subtle">
                          Active
                        </Badge>
                      )}
                    </HStack>
                  </Box>
                )}
              </FormControl>

              {/* GST Number (conditional) */}
              {formik.values.hasGst && (
                <FormControl isInvalid={formik.errors.gstNumber && formik.touched.gstNumber}>
                  <FormLabel fontWeight="semibold" color={labelColor} mb={3}>
                    GST Number
                  </FormLabel>
                  {isEditing ? (
                    <Input
                      name="gstNumber"
                      value={formik.values.gstNumber}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="Enter GST number"
                      bg={inputBg}
                      borderColor={borderColor}
                      color={textColor}
                      fontSize={{ base: "md", md: "lg" }}
                      p={{ base: 3, md: 4 }}
                      borderRadius={{ base: "lg", md: "xl" }}
                      borderWidth="2px"
                      _hover={{
                        borderColor: "orange.300"
                      }}
                      _focus={{
                        borderColor: "orange.500",
                        boxShadow: "0 0 0 1px orange.500"
                      }}
                    />
                  ) : (
                    <Box
                      p={4}
                      borderWidth="2px"
                      borderRadius="xl"
                      bg={readOnlyBg}
                      color={textColor}
                      minH="48px"
                      display="flex"
                      alignItems="center"
                      borderColor={borderColor}
                      fontSize="lg"
                      fontWeight="medium"
                      transition="all 0.2s"
                      _hover={{
                        shadow: "sm"
                      }}
                    >
                      {formik.values.gstNumber || "No GST number provided"}
                    </Box>
                  )}
                  {isEditing && <FormErrorMessage>{formik.errors.gstNumber}</FormErrorMessage>}
                </FormControl>
              )}
            </VStack>
          </CardBody>
        </Card>

        {/* Bank Details Section */}
        <Card 
          bg={cardBg}
          shadow="xl"
          borderRadius={{ base: "lg", md: "xl" }}
          border="1px"
          borderColor="teal.200"
          overflow="hidden"
          transition="all 0.3s ease"
          w="full"
          _hover={{
            shadow: "2xl",
            transform: "translateY(-2px)",
            borderColor: "teal.300"
          }}
        >
          <CardBody p={{ base: 4, md: 6 }}>
            <HStack spacing={3} mb={{ base: 4, md: 6 }}>
              <Icon as={LuBanknote} boxSize={5} color="teal.500" />
              <Heading size={{ base: "sm", md: "md" }} color={textColor} fontWeight="semibold">
                Account Details
              </Heading>
            </HStack>
            
            <VStack spacing={{ base: 4, md: 6 }} align="stretch">
              {/* Account Holder Name */}
              <FormControl isInvalid={formik.errors.accountHolderName && formik.touched.accountHolderName}>
                <FormLabel fontWeight="semibold" color={labelColor} mb={3}>
                  Account Holder Name
                </FormLabel>
                {isEditing ? (
                  <Input
                    name="accountHolderName"
                    value={formik.values.accountHolderName}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter account holder name"
                    bg={inputBg}
                    borderColor={borderColor}
                    color={textColor}
                    fontSize="lg"
                    p={4}
                    borderRadius="xl"
                    borderWidth="2px"
                    _hover={{
                      borderColor: "teal.300"
                    }}
                    _focus={{
                      borderColor: "teal.500",
                      boxShadow: "0 0 0 1px teal.500"
                    }}
                  />
                ) : (
                  <Box
                    p={4}
                    borderWidth="2px"
                    borderRadius="xl"
                    bg={readOnlyBg}
                    color={textColor}
                    minH="48px"
                    display="flex"
                    alignItems="center"
                    borderColor={borderColor}
                    fontSize="lg"
                    fontWeight="medium"
                    transition="all 0.2s"
                    _hover={{
                      shadow: "sm"
                    }}
                  >
                    {formik.values.accountHolderName || "No account holder name provided"}
                  </Box>
                )}
                {isEditing && <FormErrorMessage>{formik.errors.accountHolderName}</FormErrorMessage>}
              </FormControl>

              {/* Account Number */}
              <FormControl isInvalid={formik.errors.accountNumber && formik.touched.accountNumber}>
                <FormLabel fontWeight="semibold" color={labelColor} mb={3}>
                  Account Number
                </FormLabel>
                {isEditing ? (
                  <Input
                    name="accountNumber"
                    value={formik.values.accountNumber}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter account number"
                    bg={inputBg}
                    borderColor={borderColor}
                    color={textColor}
                    fontSize="lg"
                    p={4}
                    borderRadius="xl"
                    borderWidth="2px"
                    _hover={{
                      borderColor: "teal.300"
                    }}
                    _focus={{
                      borderColor: "teal.500",
                      boxShadow: "0 0 0 1px teal.500"
                    }}
                  />
                ) : (
                  <Box
                    p={4}
                    borderWidth="2px"
                    borderRadius="xl"
                    bg={readOnlyBg}
                    color={textColor}
                    minH="48px"
                    display="flex"
                    alignItems="center"
                    borderColor={borderColor}
                    fontSize="lg"
                    fontWeight="medium"
                    transition="all 0.2s"
                    _hover={{
                      shadow: "sm"
                    }}
                  >
                    {formik.values.accountNumber ? 
                      `****-****-${formik.values.accountNumber.slice(-4)}` : 
                      "No account number provided"
                    }
                  </Box>
                )}
                {isEditing && <FormErrorMessage>{formik.errors.accountNumber}</FormErrorMessage>}
              </FormControl>

              {/* IFSC Code */}
              <FormControl isInvalid={formik.errors.ifsc && formik.touched.ifsc}>
                <FormLabel fontWeight="semibold" color={labelColor} mb={3}>
                  IFSC Code
                </FormLabel>
                {isEditing ? (
                  <Input
                    name="ifsc"
                    value={formik.values.ifsc}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter IFSC code"
                    bg={inputBg}
                    borderColor={borderColor}
                    color={textColor}
                    fontSize="lg"
                    p={4}
                    borderRadius="xl"
                    borderWidth="2px"
                    _hover={{
                      borderColor: "teal.300"
                    }}
                    _focus={{
                      borderColor: "teal.500",
                      boxShadow: "0 0 0 1px teal.500"
                    }}
                  />
                ) : (
                  <Box
                    p={4}
                    borderWidth="2px"
                    borderRadius="xl"
                    bg={readOnlyBg}
                    color={textColor}
                    minH="48px"
                    display="flex"
                    alignItems="center"
                    borderColor={borderColor}
                    fontSize="lg"
                    fontWeight="medium"
                    transition="all 0.2s"
                    _hover={{
                      shadow: "sm"
                    }}
                  >
                    {formik.values.ifsc || "No IFSC code provided"}
                  </Box>
                )}
                {isEditing && <FormErrorMessage>{formik.errors.ifsc}</FormErrorMessage>}
              </FormControl>
            </VStack>
          </CardBody>
        </Card>

        {/* PAN Details Section */}
        <Card 
          bg={cardBg}
          shadow="xl"
          borderRadius={{ base: "lg", md: "xl" }}
          border="1px"
          borderColor="blue.200"
          overflow="hidden"
          transition="all 0.3s ease"
          w="full"
          _hover={{
            shadow: "2xl",
            transform: "translateY(-2px)",
            borderColor: "blue.300"
          }}
        >
          <CardBody p={{ base: 4, md: 6 }}>
            <HStack spacing={3} mb={{ base: 4, md: 6 }}>
              <Icon as={LuCreditCard} boxSize={5} color="blue.500" />
              <Heading size={{ base: "sm", md: "md" }} color={textColor} fontWeight="semibold">
                PAN Details
              </Heading>
            </HStack>
            
            <VStack spacing={{ base: 4, md: 6 }} align="stretch">
              {/* PAN Number */}
              <FormControl isInvalid={formik.errors.panNumber && formik.touched.panNumber}>
                <FormLabel fontWeight="semibold" color={labelColor} mb={{ base: 2, md: 3 }} fontSize={{ base: "sm", md: "md" }}>
                  PAN Number
                </FormLabel>
                {isEditing ? (
                  <Input
                    name="panNumber"
                    value={formik.values.panNumber}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter PAN number"
                    bg={inputBg}
                    borderColor={borderColor}
                    color={textColor}
                    fontSize={{ base: "sm", md: "lg" }}
                    p={{ base: 3, md: 4 }}
                    borderRadius={{ base: "lg", md: "xl" }}
                    borderWidth="2px"
                    size={{ base: "md", md: "lg" }}
                    _hover={{
                      borderColor: "blue.300"
                    }}
                    _focus={{
                      borderColor: "blue.500",
                      boxShadow: "0 0 0 1px blue.500"
                    }}
                  />
                ) : (
                  <Box
                    p={{ base: 3, md: 4 }}
                    borderWidth="2px"
                    borderRadius={{ base: "lg", md: "xl" }}
                    bg={readOnlyBg}
                    color={textColor}
                    minH={{ base: "44px", md: "48px" }}
                    display="flex"
                    alignItems="center"
                    borderColor={borderColor}
                    fontSize={{ base: "sm", md: "lg" }}
                    fontWeight="medium"
                    transition="all 0.2s"
                    _hover={{
                      shadow: "sm"
                    }}
                  >
                    {formik.values.panNumber || "No PAN number provided"}
                  </Box>
                )}
                {isEditing && <FormErrorMessage>{formik.errors.panNumber}</FormErrorMessage>}
              </FormControl>

              {/* PAN Image */}
              <FormControl isInvalid={formik.errors.panImage && formik.touched.panImage}>
                <FormLabel fontWeight="semibold" color={labelColor} mb={{ base: 2, md: 3 }} fontSize={{ base: "sm", md: "md" }}>
                  PAN Card Image
                </FormLabel>
                {isEditing ? (
                  <>
                    <Input
                      type="file"
                      id="panImageInput"
                      onChange={(e) => handleFileChange(e, "panImage", 0)}
                      style={{ display: "none" }}
                      accept="image/*"
                    />
                    <VStack spacing={{ base: 3, md: 4 }} align="stretch">
                      <HStack spacing={{ base: 2, md: 3 }} flexDirection={{ base: "column", md: "row" }}>
                        <Button
                          onClick={() => document.getElementById('panImageInput').click()}
                          colorScheme="blue"
                          variant="outline"
                          borderRadius={{ base: "lg", md: "xl" }}
                          size={{ base: "md", md: "lg" }}
                          fontSize={{ base: "sm", md: "md" }}
                          w={{ base: "full", md: "auto" }}
                        >
                          Upload PAN Image
                        </Button>
                      </HStack>
                      {formik.values.panImage.length > 0 && (
                        <VStack spacing={{ base: 3, md: 4 }} align="stretch">
                          {formik.values.panImage.map((imageUrl, index) => (
                            <Box
                              key={index}
                              p={{ base: 3, md: 4 }}
                              borderWidth="2px"
                              borderRadius={{ base: "lg", md: "xl" }}
                              borderColor={borderColor}
                              bg={inputBg}
                              transition="all 0.2s"
                              _hover={{
                                shadow: "sm"
                              }}
                            >
                              <HStack spacing={{ base: 2, md: 3 }} mb={{ base: 2, md: 3 }} justify="space-between">
                                <HStack spacing={{ base: 2, md: 3 }}>
                                  <Icon as={LuBadgeCheck} color="green.500" />
                                  <Text fontWeight="medium" color={textColor} fontSize={{ base: "sm", md: "md" }}>
                                    PAN Card {index + 1}
                                  </Text>
                                </HStack>
                                <Button
                                  colorScheme="red"
                                  variant="outline"
                                  size="sm"
                                  onClick={() => deleteImageFiles("panImage", index)}
                                >
                                  Remove
                                </Button>
                              </HStack>
                              <a href={imageUrl} target="_blank" rel="noopener noreferrer">
                                <Image
                                  src={imageUrl}
                                  alt={`PAN Image ${index + 1} Preview`}
                                  maxW={{ base: "150px", md: "200px" }}
                                  maxH={{ base: "112px", md: "150px" }}
                                  objectFit="cover"
                                  borderRadius={{ base: "md", md: "lg" }}
                                  border="2px"
                                  borderColor="blue.200"
                                  transition="all 0.2s"
                                  _hover={{
                                    borderColor: "blue.400",
                                    transform: "scale(1.02)"
                                  }}
                                  cursor="pointer"
                                />
                              </a>
                            </Box>
                          ))}
                        </VStack>
                      )}
                    </VStack>
                  </>
                ) : (
                  <>
                    {formik.values.panImage.length > 0 ? (
                      <VStack spacing={{ base: 3, md: 4 }} align="stretch">
                        {formik.values.panImage.map((imageUrl, index) => (
                          <Box
                            key={index}
                            p={{ base: 3, md: 4 }}
                            borderWidth="2px"
                            borderRadius={{ base: "lg", md: "xl" }}
                            borderColor={borderColor}
                            bg={readOnlyBg}
                            transition="all 0.2s"
                            _hover={{
                              shadow: "sm"
                            }}
                          >
                            <HStack spacing={{ base: 2, md: 3 }} mb={{ base: 2, md: 3 }}>
                              <Icon as={LuBadgeCheck} color="green.500" />
                              <Text fontWeight="medium" color={textColor} fontSize={{ base: "sm", md: "md" }}>
                                PAN Card {index + 1}
                              </Text>
                            </HStack>
                            <a href={imageUrl} target="_blank" rel="noopener noreferrer">
                              <Image
                                src={imageUrl}
                                alt={`PAN Image ${index + 1} Preview`}
                                maxW={{ base: "150px", md: "200px" }}
                                maxH={{ base: "112px", md: "150px" }}
                                objectFit="cover"
                                borderRadius={{ base: "md", md: "lg" }}
                                border="2px"
                                borderColor="blue.200"
                                transition="all 0.2s"
                                _hover={{
                                  borderColor: "blue.400",
                                  transform: "scale(1.02)"
                                }}
                                cursor="pointer"
                              />
                            </a>
                          </Box>
                        ))}
                      </VStack>
                    ) : (
                      <Box
                        p={{ base: 3, md: 4 }}
                        borderWidth="2px"
                        borderRadius={{ base: "lg", md: "xl" }}
                        bg={readOnlyBg}
                        color={textColor}
                        minH={{ base: "44px", md: "48px" }}
                        display="flex"
                        alignItems="center"
                        borderColor={borderColor}
                        fontSize={{ base: "sm", md: "lg" }}
                        fontWeight="medium"
                        transition="all 0.2s"
                        _hover={{
                          shadow: "sm"
                        }}
                      >
                        No PAN card image uploaded
                      </Box>
                    )}
                  </>
                )}
                {isEditing && <FormErrorMessage>{formik.errors.panImage}</FormErrorMessage>}
              </FormControl>
            </VStack>
          </CardBody>
        </Card>

          {/* Aadhaar Details Section */}
          <Card 
            bg={cardBg}
            shadow="xl"
            borderRadius={{ base: "lg", md: "xl" }}
            border="1px"
            borderColor="purple.200"
            overflow="hidden"
            transition="all 0.3s ease"
            w="full"
            _hover={{
              shadow: "2xl",
              transform: "translateY(-2px)",
              borderColor: "purple.300"
            }}
          >
            <CardBody p={{ base: 4, md: 6 }}>
              <HStack spacing={3} mb={{ base: 4, md: 6 }}>
                <Icon as={LuFileText} boxSize={5} color="purple.500" />
                <Heading size={{ base: "sm", md: "md" }} color={textColor} fontWeight="semibold">
                  Aadhaar Details
                </Heading>
              </HStack>
              
              <VStack spacing={{ base: 4, md: 6 }} align="stretch">
                {/* Aadhaar Number */}
                <FormControl isInvalid={formik.errors.aadhaarNumber && formik.touched.aadhaarNumber}>
                  <FormLabel fontWeight="semibold" color={labelColor} mb={{ base: 2, md: 3 }} fontSize={{ base: "sm", md: "md" }}>
                    Aadhaar Number
                  </FormLabel>
                  {isEditing ? (
                    <Input
                      name="aadhaarNumber"
                      value={formik.values.aadhaarNumber}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="Enter Aadhaar number"
                      bg={inputBg}
                      borderColor={borderColor}
                      color={textColor}
                      fontSize={{ base: "sm", md: "lg" }}
                      p={{ base: 3, md: 4 }}
                      borderRadius={{ base: "lg", md: "xl" }}
                      borderWidth="2px"
                      size={{ base: "md", md: "lg" }}
                      _hover={{
                        borderColor: "purple.300"
                      }}
                      _focus={{
                        borderColor: "purple.500",
                        boxShadow: "0 0 0 1px purple.500"
                      }}
                    />
                  ) : (
                    <Box
                      p={{ base: 3, md: 4 }}
                      borderWidth="2px"
                      borderRadius={{ base: "lg", md: "xl" }}
                      bg={readOnlyBg}
                      color={textColor}
                      minH={{ base: "44px", md: "48px" }}
                      display="flex"
                      alignItems="center"
                      borderColor={borderColor}
                      fontSize={{ base: "sm", md: "lg" }}
                      fontWeight="medium"
                      transition="all 0.2s"
                      _hover={{
                        shadow: "sm"
                      }}
                    >
                      {formik.values.aadhaarNumber ? `****-****-${formik.values.aadhaarNumber.slice(-4)}` : "No Aadhaar number provided"}
                    </Box>
                  )}
                  {isEditing && <FormErrorMessage>{formik.errors.aadhaarNumber}</FormErrorMessage>}
                </FormControl>

                {/* Aadhaar Images */}
                <FormControl isInvalid={formik.errors.aadhaarImage && formik.touched.aadhaarImage}>
                  <FormLabel fontWeight="semibold" color={labelColor} mb={{ base: 2, md: 3 }} fontSize={{ base: "sm", md: "md" }}>
                    Aadhaar Card Images (Front & Back)
                  </FormLabel>
                  {isEditing ? (
                    <>
                      <VStack spacing={{ base: 3, md: 4 }} align="stretch">
                        {/* Front Image */}
                        <Box>
                          <Text fontWeight="medium" color={labelColor} mb={2} fontSize={{ base: "sm", md: "md" }}>
                            Front Side
                          </Text>
                          <Input
                            type="file"
                            id="aadhaarImageFrontInput"
                            onChange={(e) => handleFileChange(e, "aadhaarImage", 0)}
                            style={{ display: "none" }}
                            accept="image/*"
                          />
                          <HStack spacing={{ base: 2, md: 3 }} flexDirection={{ base: "column", md: "row" }} mb={3}>
                            <Button
                              onClick={() => document.getElementById('aadhaarImageFrontInput').click()}
                              colorScheme="purple"
                              variant="outline"
                              borderRadius={{ base: "lg", md: "xl" }}
                              size={{ base: "md", md: "lg" }}
                              fontSize={{ base: "sm", md: "md" }}
                              w={{ base: "full", md: "auto" }}
                              isDisabled={formik.values.aadhaarImage[0]}
                            >
                              Upload Front Image
                            </Button>
                            {formik.values.aadhaarImage[0] && (
                              <Button
                                colorScheme="red"
                                variant="outline"
                                onClick={() => deleteImageFiles("aadhaarImage", 0)}
                                borderRadius={{ base: "lg", md: "xl" }}
                                size={{ base: "md", md: "lg" }}
                                fontSize={{ base: "sm", md: "md" }}
                                w={{ base: "full", md: "auto" }}
                              >
                                Remove Front
                              </Button>
                            )}
                          </HStack>
                          {formik.values.aadhaarImage[0] && (
                            <Box
                              p={{ base: 3, md: 4 }}
                              borderWidth="2px"
                              borderRadius={{ base: "lg", md: "xl" }}
                              borderColor={borderColor}
                              bg={inputBg}
                              transition="all 0.2s"
                              _hover={{
                                shadow: "sm"
                              }}
                            >
                              <HStack spacing={{ base: 2, md: 3 }} mb={{ base: 2, md: 3 }}>
                                <Icon as={LuBadgeCheck} color="green.500" />
                                <Text fontWeight="medium" color={textColor} fontSize={{ base: "sm", md: "md" }}>
                                  Aadhaar Front Uploaded
                                </Text>
                              </HStack>
                              <a href={formik.values.aadhaarImage[0]} target="_blank" rel="noopener noreferrer">
                                <Image
                                  src={formik.values.aadhaarImage[0]}
                                  alt="Aadhaar Front Image Preview"
                                  maxW={{ base: "150px", md: "200px" }}
                                  maxH={{ base: "112px", md: "150px" }}
                                  objectFit="cover"
                                  borderRadius={{ base: "md", md: "lg" }}
                                  border="2px"
                                  borderColor="purple.200"
                                  transition="all 0.2s"
                                  _hover={{
                                    borderColor: "purple.400",
                                    transform: "scale(1.02)"
                                  }}
                                  cursor="pointer"
                                />
                              </a>
                            </Box>
                          )}
                        </Box>

                        {/* Back Image */}
                        <Box>
                          <Text fontWeight="medium" color={labelColor} mb={2} fontSize={{ base: "sm", md: "md" }}>
                            Back Side
                          </Text>
                          <Input
                            type="file"
                            id="aadhaarImageBackInput"
                            onChange={(e) => handleFileChange(e, "aadhaarImage", 1)}
                            style={{ display: "none" }}
                            accept="image/*"
                          />
                          <HStack spacing={{ base: 2, md: 3 }} flexDirection={{ base: "column", md: "row" }} mb={3}>
                            <Button
                              onClick={() => document.getElementById('aadhaarImageBackInput').click()}
                              colorScheme="purple"
                              variant="outline"
                              borderRadius={{ base: "lg", md: "xl" }}
                              size={{ base: "md", md: "lg" }}
                              fontSize={{ base: "sm", md: "md" }}
                              w={{ base: "full", md: "auto" }}
                              isDisabled={formik.values.aadhaarImage[1]}
                            >
                              Upload Back Image
                            </Button>
                            {formik.values.aadhaarImage[1] && (
                              <Button
                                colorScheme="red"
                                variant="outline"
                                onClick={() => deleteImageFiles("aadhaarImage", 1)}
                                borderRadius={{ base: "lg", md: "xl" }}
                                size={{ base: "md", md: "lg" }}
                                fontSize={{ base: "sm", md: "md" }}
                                w={{ base: "full", md: "auto" }}
                              >
                                Remove Back
                              </Button>
                            )}
                          </HStack>
                          {formik.values.aadhaarImage[1] && (
                            <Box
                              p={{ base: 3, md: 4 }}
                              borderWidth="2px"
                              borderRadius={{ base: "lg", md: "xl" }}
                              borderColor={borderColor}
                              bg={inputBg}
                              transition="all 0.2s"
                              _hover={{
                                shadow: "sm"
                              }}
                            >
                              <HStack spacing={{ base: 2, md: 3 }} mb={{ base: 2, md: 3 }}>
                                <Icon as={LuBadgeCheck} color="green.500" />
                                <Text fontWeight="medium" color={textColor} fontSize={{ base: "sm", md: "md" }}>
                                  Aadhaar Back Uploaded
                                </Text>
                              </HStack>
                              <a href={formik.values.aadhaarImage[1]} target="_blank" rel="noopener noreferrer">
                                <Image
                                  src={formik.values.aadhaarImage[1]}
                                  alt="Aadhaar Back Image Preview"
                                  maxW={{ base: "150px", md: "200px" }}
                                  maxH={{ base: "112px", md: "150px" }}
                                  objectFit="cover"
                                  borderRadius={{ base: "md", md: "lg" }}
                                  border="2px"
                                  borderColor="purple.200"
                                  transition="all 0.2s"
                                  _hover={{
                                    borderColor: "purple.400",
                                    transform: "scale(1.02)"
                                  }}
                                  cursor="pointer"
                                />
                              </a>
                            </Box>
                          )}
                        </Box>
                      </VStack>
                    </>
                  ) : (
                    <>
                      <VStack spacing={{ base: 3, md: 4 }} align="stretch">
                        {/* Display Front Image */}
                        {formik.values.aadhaarImage[0] ? (
                          <Box>
                            <Text fontWeight="medium" color={labelColor} mb={2} fontSize={{ base: "sm", md: "md" }}>
                              Front Side
                            </Text>
                            <Box
                              p={{ base: 3, md: 4 }}
                              borderWidth="2px"
                              borderRadius={{ base: "lg", md: "xl" }}
                              borderColor={borderColor}
                              bg={readOnlyBg}
                              transition="all 0.2s"
                              _hover={{
                                shadow: "sm"
                              }}
                            >
                              <HStack spacing={{ base: 2, md: 3 }} mb={{ base: 2, md: 3 }}>
                                <Icon as={LuBadgeCheck} color="green.500" />
                                <Text fontWeight="medium" color={textColor} fontSize={{ base: "sm", md: "md" }}>
                                  Aadhaar Front Uploaded
                                </Text>
                              </HStack>
                              <a href={formik.values.aadhaarImage[0]} target="_blank" rel="noopener noreferrer">
                                <Image
                                  src={formik.values.aadhaarImage[0]}
                                  alt="Aadhaar Front Image Preview"
                                  maxW={{ base: "150px", md: "200px" }}
                                  maxH={{ base: "112px", md: "150px" }}
                                  objectFit="cover"
                                  borderRadius={{ base: "md", md: "lg" }}
                                  border="2px"
                                  borderColor="purple.200"
                                  transition="all 0.2s"
                                  _hover={{
                                    borderColor: "purple.400",
                                    transform: "scale(1.02)"
                                  }}
                                  cursor="pointer"
                                />
                              </a>
                            </Box>
                          </Box>
                        ) : null}

                        {/* Display Back Image */}
                        {formik.values.aadhaarImage[1] ? (
                          <Box>
                            <Text fontWeight="medium" color={labelColor} mb={2} fontSize={{ base: "sm", md: "md" }}>
                              Back Side
                            </Text>
                            <Box
                              p={{ base: 3, md: 4 }}
                              borderWidth="2px"
                              borderRadius={{ base: "lg", md: "xl" }}
                              borderColor={borderColor}
                              bg={readOnlyBg}
                              transition="all 0.2s"
                              _hover={{
                                shadow: "sm"
                              }}
                            >
                              <HStack spacing={{ base: 2, md: 3 }} mb={{ base: 2, md: 3 }}>
                                <Icon as={LuBadgeCheck} color="green.500" />
                                <Text fontWeight="medium" color={textColor} fontSize={{ base: "sm", md: "md" }}>
                                  Aadhaar Back Uploaded
                                </Text>
                              </HStack>
                              <a href={formik.values.aadhaarImage[1]} target="_blank" rel="noopener noreferrer">
                                <Image
                                  src={formik.values.aadhaarImage[1]}
                                  alt="Aadhaar Back Image Preview"
                                  maxW={{ base: "150px", md: "200px" }}
                                  maxH={{ base: "112px", md: "150px" }}
                                  objectFit="cover"
                                  borderRadius={{ base: "md", md: "lg" }}
                                  border="2px"
                                  borderColor="purple.200"
                                  transition="all 0.2s"
                                  _hover={{
                                    borderColor: "purple.400",
                                    transform: "scale(1.02)"
                                  }}
                                  cursor="pointer"
                                />
                              </a>
                            </Box>
                          </Box>
                        ) : null}

                        {/* Show message if no images */}
                        {!formik.values.aadhaarImage[0] && !formik.values.aadhaarImage[1] && (
                          <Box
                            p={{ base: 3, md: 4 }}
                            borderWidth="2px"
                            borderRadius={{ base: "lg", md: "xl" }}
                            bg={readOnlyBg}
                            color={textColor}
                            minH={{ base: "44px", md: "48px" }}
                            display="flex"
                            alignItems="center"
                            borderColor={borderColor}
                            fontSize={{ base: "sm", md: "lg" }}
                            fontWeight="medium"
                            transition="all 0.2s"
                            _hover={{
                              shadow: "sm"
                            }}
                          >
                            No Aadhaar card images uploaded
                          </Box>
                        )}
                      </VStack>
                    </>
                  )}
                  {isEditing && <FormErrorMessage>{formik.errors.aadhaarImage}</FormErrorMessage>}
                </FormControl>
              </VStack>
            </CardBody>
          </Card>
        </VStack>
      </ScaleFade>
    );
  };

export default KYCDetailsAcademy; 
