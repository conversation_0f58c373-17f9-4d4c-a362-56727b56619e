import classNames from "classnames";
import { Dropdown } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import {
  Icon,
  MediaGroup,
  MediaText,
  LinkList,
  CustomDropdownMenu,
} from "../../../components";

import Menu from "./Menu";

import ToggleSidebar from "../Toggle/Sidebar";
import ToggleNavbar from "../Toggle/Navbar";

import { useLayout, useLayoutUpdate } from "./../LayoutProvider";
import { deleteCookie, getCookie } from "../../../utilities/auth";

function QuickNav({ className, ...props }) {
  const compClass = classNames({
    "nk-quick-nav": true,
    [className]: className,
  });
  return <ul className={compClass}>{props.children}</ul>;
}

function QuickNavItem({ className, ...props }) {
  const compClass = classNames({
    "d-inline-flex": true,
    [className]: className,
  });
  return <li className={compClass}>{props.children}</li>;
}

function Header() {
  const layout = useLayout();
  const navigate = useNavigate();

  const compClass = classNames({
    "nk-header nk-header-fixed": true,
    [`is-${layout.headerVariant}`]: layout.headerVariant,
  });

  return (
    <div
      className={compClass}
      style={{ display: "flex", justifyContent: "space-between", flexDirection: "row", alignItems: "center" }}
    >
      <div style={{ marginLeft: 15 }}>
        <span style={{ fontWeight: 700, fontSize: 22, color: "#222" }}>
          Admin Dashboard
        </span>
      </div>
      <div>
        <QuickNav>
          <Dropdown as={QuickNavItem}>
            <Dropdown.Toggle
              variant="zoom"
              size="sm"
              bsPrefix
              className="btn-icon d-sm-none"
            >
              <Icon
                style={{ width: "30px", height: "30px" }}
                className="text-blue"
                name="user-alt"
              ></Icon>
            </Dropdown.Toggle>
            <Dropdown.Toggle
              variant="zoom"
              size="md"
              bsPrefix
              className="btn-icon d-none d-sm-inline-flex"
            >
              <Icon
                style={{ width: "30px", height: "30px" }}
                name="user-alt"
              ></Icon>
            </Dropdown.Toggle>
            <Dropdown.Menu className="dropdown-menu-md" as={CustomDropdownMenu}>
              <div className="dropdown-content dropdown-content-x-lg py-3 border-bottom border-light">
                <MediaGroup>
                  <MediaText>
                    <div className="lead-text">{getCookie("userName") || ""}</div>
                  </MediaText>
                </MediaGroup>
              </div>
              <div className="dropdown-content dropdown-content-x-lg py-3">
                <LinkList>
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      alignItems: "center",
                      cursor: "pointer",
                    }}
                    onClick={() => {
                      sessionStorage.removeItem("admintoken");
                      deleteCookie("userName");
                      navigate("/login"); 
                    }}
                  >
                    <Icon name="signout"></Icon>
                    <p style={{ marginLeft: "5px" }}>Log Out</p>
                  </div>
                </LinkList>
              </div>
            </Dropdown.Menu>
          </Dropdown>
        </QuickNav>
      </div>
    </div>
  );
}

export default Header;
