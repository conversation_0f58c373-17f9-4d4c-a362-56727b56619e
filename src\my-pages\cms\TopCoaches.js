import React, { useEffect, useState } from "react";
import Layout from "../../layout/default";
import { Md<PERSON><PERSON><PERSON>, MdDrag<PERSON>andle } from "react-icons/md";
import {
  Box,
  BreadcrumbLink,
  Input,
  Breadcrumb,
  BreadcrumbItem,
  Card,
  CardBody,
  Button,
  Stack,
  Text,
  Flex,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  TableContainer,
  Table,
  Tbody,
  Tr,
  Td,
  Thead,
  Th,
  ListItem,
  UnorderedList,
  useToast,
  Spinner,
  Heading,
  AlertDialog,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogCloseButton,
  AlertDialogBody,
  AlertDialogFooter,
  Badge,
  Tooltip,
} from "@chakra-ui/react";
import { Link } from "react-router-dom";
import axios from "axios";
import { getCookie } from "../../utilities/auth";
import { useSelector } from "react-redux";

const TopCoaches = () => {
  const [coachData, setCoachData] = useState({
    result: [],
    isLoading: false,
    error: false,
  });
  const [cmsCoachData, setCmsCoachData] = useState({
    result: [],
    isLoading: false,
    error: false,
  });

  const [isUpdated, setIsUpdated] = useState(false);
  const [draggedItemIndex, setDraggedItemIndex] = useState(null);
  const [saveChangesBtnLoading, setSaveChangesBtnLoading] = useState(false);
  const [adjustBtnEdit, setAdjustBtnEdit] = useState(false);
  const [prevCoachData, setPrevCoachData] = useState([]);
  const [posBtnLoading, setPosBtnLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [isDeleted, setIsDeleted] = useState(false);
  const { isOpen, onOpen, onClose } = useDisclosure();

  const [isOpen1, setIsOpen1] = useState(false);
  const onClose1 = () => setIsOpen1(false);
  const onOpen1 = () => setIsOpen1(true);

  const toast = useToast();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const userData = useSelector((state) => state.user);

  const getCoachData = (query) => {
    setCoachData({ result: [], isLoading: true, error: false });
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/coach?${
        searchQuery.length > 0
          ? `page=1&firstName=${query}&authStatus=authorized&status=active`
          : "page=1&authStatus=authorized&status=active"
      }`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        setCoachData({
          result: response.data.data,
          isLoading: false,
          error: false,
        });
      })
      .catch((error) => {
        console.log(error);
        setCoachData({ result: [], isLoading: false, error: true });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const getCmsCoachData = () => {
    setCmsCoachData({ result: [], isLoading: true, error: false });
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/cms/coach`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };
    axios
      .request(config)
      .then((response) => {
        setCmsCoachData({
          result: response.data.sort((a, b) => a.position - b.position),
          isLoading: false,
          error: false,
        });
      })
      .catch((error) => {
        console.log(error);
        setCmsCoachData({ result: [], isLoading: false, error: true });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const updateAndSave = () => {
    onClose1();
    setSaveChangesBtnLoading(true);
    const getIds = cmsCoachData.result.map((id, index) => {
      return { coach: id?.coach?._id, position: index + 1 };
    });

    let data = JSON.stringify({
      documents: getIds,
    });
    let config = {
      method: "post",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/cms/coach/add`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
     ;
        getCmsCoachData();
        getCoachData(searchQuery);
        setSaveChangesBtnLoading(false);
        setIsUpdated(false);
        onClose();
        setIsDeleted(false);
        toast({
          title: "Coach updated successfully",
          status: "success",
          duration: 3500,
          position: "top",
          isClosable: true,
        });
      })
      .catch((error) => {
        console.log(error);
        setSaveChangesBtnLoading(false);
        if (
          error.response.data.message === "Top Coach cannot be greater than 15"
        ) {
          getCmsCoachData();
          setIsUpdated(false);
          toast({
            title: error.response.data.message,
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const handleDragStart = (index) => {
    setDraggedItemIndex(index);
  };

  const handleDragOver = (index) => {
    if (draggedItemIndex === null) return;
    const newItems = [...cmsCoachData.result]; // Copy the result array from blockData
    const draggedItem = newItems[draggedItemIndex];
    newItems.splice(draggedItemIndex, 1);
    newItems.splice(index, 0, draggedItem);

    // Adjust positions of items inside newItems array
    newItems.forEach((item, idx) => {
      item.position = idx + 1;
    });

    setCmsCoachData({ ...cmsCoachData, result: newItems }); // Update result array in blockData
    setDraggedItemIndex(index);
  };

  const handleDragEnd = () => {
    setDraggedItemIndex(null);
  };

  const updateBlockPosition = () => {
    setPosBtnLoading(true);
    const value = cmsCoachData.result.map((x) => {
      return { id: x._id, newPosition: x.position };
    });
    let data = JSON.stringify({
      updates: value,
    });

    let config = {
      method: "patch",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/cms/coach/position`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
        setPosBtnLoading(false);
        setPrevCoachData([]);
        setAdjustBtnEdit(false);
        toast({
          title: "Coaches position updated",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      })
      .catch((error) => {
        console.log(error);
        onClose1();
        setPosBtnLoading(false);
        setPrevCoachData([]);
        setAdjustBtnEdit(false);
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  useEffect(() => {
    getCmsCoachData();
  }, []);

  return (
    <Layout title="CMS | Top Coach" content="container">
      <Flex justifyContent={"space-between"} alignItems={"center"}>
        <Breadcrumb fontWeight="medium" fontSize="sm">
          <BreadcrumbItem>
            <BreadcrumbLink>
              <Link to={"/"}>Dashboard</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>

          <BreadcrumbItem isCurrentPage>
            <BreadcrumbLink>CMS</BreadcrumbLink>
          </BreadcrumbItem>

          <BreadcrumbItem isCurrentPage>
            <BreadcrumbLink href="#">Top Coaches</BreadcrumbLink>
          </BreadcrumbItem>
        </Breadcrumb>
        {userData?.accessScopes?.cms?.includes("write") && (
          <>
          {isDeleted ? (
              !(!cmsCoachData?.isLoading && cmsCoachData?.error) &&
              !adjustBtnEdit &&
              userData?.accessScopes?.cms?.includes("write") && (
                <Flex justifyContent={"flex-end"} alignItems={"center"}>
                  <Button
                    variant={"outline"}
                    colorScheme="red"
                    size={"sm"}
                    py={5}
                    px={4}
                    mr={4}
                    isDisabled={!isUpdated}
                    onClick={() => {
                      setIsDeleted(false);
                      getCmsCoachData();
                      setIsUpdated(false);
                    }}
                  >
                    Discard
                  </Button>
                  <Button
                    variant={"outline"}
                    colorScheme="green"
                    size={"sm"}
                    py={5}
                    px={4}
                    isDisabled={!isUpdated}
                    onClick={onOpen1}
                    isLoading={saveChangesBtnLoading}
                  >
                    Save Changes
                  </Button>
                </Flex>
              )
            ) : (
            <Flex>
              {!adjustBtnEdit ? (
                <Box>
                  <Button
                    variant={"outline"}
                    colorScheme="telegram"
                    size={"sm"}
                    py={5}
                    px={4}
                    mr={3}
                    onClick={() => {
                      setAdjustBtnEdit(true);
                      setPrevCoachData(cmsCoachData.result);
                    }}
                  >
                    Adjust Position
                  </Button>
                  <Button
                    variant={"outline"}
                    colorScheme="teal"
                    size={"sm"}
                    py={5}
                    px={4}
                    onClick={() => {
                      onOpen();
                      getCoachData("");
                    }}
                    mr={2}
                    isDisabled={!cmsCoachData?.isLoading && cmsCoachData?.error}
                  >
                    Add Coach
                  </Button>
                </Box>
              ) : (
                <Flex>
                  <Button
                    variant={"outline"}
                    colorScheme="red"
                    size={"sm"}
                    py={5}
                    px={4}
                    mr={4}
                    onClick={() => {
                      setCmsCoachData({
                        result: prevCoachData,
                        isLoading: false,
                        error: false,
                      });
                      setAdjustBtnEdit(false);
                    }}
                  >
                    Discard
                  </Button>
                  <Button
                    variant={"outline"}
                    colorScheme="green"
                    size={"sm"}
                    py={5}
                    px={4}
                    isLoading={posBtnLoading}
                    onClick={updateBlockPosition}
                  >
                    Save Changes
                  </Button>
                </Flex>
              )}
            </Flex>
        )}
          </>
        )}
      </Flex>
      {/* Added/Selected Course List */}
      {!cmsCoachData?.isLoading && cmsCoachData?.error ? (
        <Flex
          justifyContent={"center"}
          alignItems={"center"}
          w={"full"}
          my={10}
        >
          <Text color={"red.500"}>
            Something went wrong please try again later...
          </Text>
        </Flex>
      ) : (
        <TableContainer
          mt={6}
          height={`${window.innerHeight - 200}px`}
          overflowY={"scroll"}
        >
          <Table variant="simple">
            <Thead
              bgColor={"#c1eaee"}
              position={"sticky"}
              top={"0px"}
              zIndex={"99"}
            >
              <Tr bgColor={"#E2DFDF"}>
                <Th>Position</Th>
                <Th>Coach Name</Th>
                <Th>Categories</Th>
                <Th>Status</Th>
                <Th>Auth Status</Th>
                <Th>Experience</Th>
                <Th textAlign={"center"}>Action</Th>
              </Tr>
            </Thead>
            <Tbody>
              {cmsCoachData?.isLoading && !cmsCoachData?.error ? (
                <Tr>
                  <Td></Td>
                  <Td> </Td>
                  <Td></Td>
                  <Td
                    display={"flex"}
                    justifyContent={"center"}
                    alignItems={"center"}
                  >
                    <Spinner />
                  </Td>
                  <Td></Td>
                  <Td></Td>
                  <Td></Td>
                </Tr>
              ) : (
                cmsCoachData?.result?.map((coachData, inx) => {
                  return (
                    <>
                      {adjustBtnEdit ? (
                        <Tr
                          key={coachData._id}
                          draggable
                          onDragStart={() => handleDragStart(inx)}
                          onDragOver={() => handleDragOver(inx)}
                          onDragEnd={handleDragEnd}
                        >
                          <Td fontSize={"14px"}>{inx + 1 + "."}</Td>
                          <Td
                            style={{
                              whiteSpace: "pre-wrap",
                              wordWrap: "break-word",
                            }}
                            fontSize={"14px"}
                          >
                            {coachData?.coach?.firstName +
                              " " +
                              coachData?.coach?.lastName}
                          </Td>
                          <Td fontSize={"14px"}>
                            {coachData?.coach?.sportsCategories?.length > 0 ? (
                              <UnorderedList>
                                {coachData?.coach?.sportsCategories.map(
                                  (cat, indx) => (
                                    <ListItem key={indx}>{cat}</ListItem>
                                  )
                                )}
                              </UnorderedList>
                            ) : (
                              "n/a"
                            )}
                          </Td>
                          <Td fontSize={"14px"}>
                            {" "}
                            <Badge
                              colorScheme={
                                coachData?.coach?.status === "active"
                                  ? "green"
                                  : "red"
                              }
                            >
                              {coachData?.coach?.status?.toUpperCase()}
                            </Badge>
                          </Td>
                          <Td fontSize={"14px"}>
                            {" "}
                            <Badge
                              colorScheme={
                                coachData?.coach?.authStatus === "authorized"
                                  ? "green"
                                  : "red"
                              }
                            >
                              {coachData?.coach?.authStatus?.toUpperCase()}
                            </Badge>
                          </Td>
                          <Td fontSize={"14px"}>
                            {coachData?.coach?.experience
                              ? Math.trunc(coachData?.coach?.experience) +
                                " Years"
                              : "n/a"}
                          </Td>
                          <Td fontSize={"14px"}>
                            {!adjustBtnEdit ? (
                              userData?.accessScopes?.cms?.includes(
                                "delete"
                              ) && (
                                <Flex
                                  justifyContent={"center"}
                                  alignItems={"center"}
                                  cursor={"pointer"}
                                  onClick={() => {
                                    setIsUpdated(true);
                                    setCmsCoachData((prevState) => ({
                                      ...prevState,
                                      result: prevState?.result.filter(
                                        (obj) =>
                                          obj?.coach?._id !==
                                          coachData?.coach?._id
                                      ),
                                    }));
                                  }}
                                >
                                  <Tooltip label="Delete Coach">
                                    <Text>
                                      <MdDelete fontSize={"24px"} />
                                    </Text>
                                  </Tooltip>
                                </Flex>
                              )
                            ) : (
                              <Flex
                                justifyContent={"center"}
                                alignItems={"center"}
                              >
                                <Text as={"span"} ml={3}>
                                  <MdDragHandle
                                    style={{ cursor: "grab" }}
                                    fontSize={"24px"}
                                  />
                                </Text>
                              </Flex>
                            )}
                          </Td>
                        </Tr>
                      ) : (
                        <Tr key={coachData._id}>
                          <Td fontSize={"14px"}>{inx + 1 + "."}</Td>
                          <Td
                            style={{
                              whiteSpace: "pre-wrap",
                              wordWrap: "break-word",
                            }}
                            fontSize={"14px"}
                          >
                            {coachData?.coach?.firstName +
                              " " +
                              coachData?.coach?.lastName}
                          </Td>
                          <Td fontSize={"14px"}>
                            {coachData?.coach?.sportsCategories?.length > 0 ? (
                              <UnorderedList>
                                {coachData?.coach?.sportsCategories.map(
                                  (cat, indx) => (
                                    <ListItem key={indx}>{cat}</ListItem>
                                  )
                                )}
                              </UnorderedList>
                            ) : (
                              "n/a"
                            )}
                          </Td>
                          <Td fontSize={"14px"}>
                            {" "}
                            <Badge
                              colorScheme={
                                coachData?.coach?.status === "active"
                                  ? "green"
                                  : "red"
                              }
                            >
                              {coachData?.coach?.status?.toUpperCase()}
                            </Badge>
                          </Td>
                          <Td fontSize={"14px"}>
                            {" "}
                            <Badge
                              colorScheme={
                                coachData?.coach?.authStatus === "authorized"
                                  ? "green"
                                  : "red"
                              }
                            >
                              {coachData?.coach?.authStatus?.toUpperCase()}
                            </Badge>
                          </Td>
                          <Td fontSize={"14px"}>
                            {coachData?.coach?.experience
                              ? coachData?.coach?.experience + " Years"
                              : "n/a"}
                          </Td>
                          <Td fontSize={"14px"}>
                            {!adjustBtnEdit ? (
                              userData?.accessScopes?.cms?.includes(
                                "delete"
                              ) && (
                                <Flex
                                  justifyContent={"center"}
                                  alignItems={"center"}
                                  cursor={"pointer"}
                                  onClick={() => {
                                    setIsUpdated(true);
                                    setIsDeleted(true);
                                    setCmsCoachData((prevState) => ({
                                      ...prevState,
                                      result: prevState?.result.filter(
                                        (obj) =>
                                          obj?.coach?._id !==
                                          coachData?.coach?._id
                                      ),
                                    }));
                                  }}
                                >
                                  <Tooltip label="Delete Coach">
                                    <Text>
                                      <MdDelete fontSize={"24px"} />
                                    </Text>
                                  </Tooltip>
                                </Flex>
                              )
                            ) : (
                              <Flex
                                justifyContent={"center"}
                                alignItems={"center"}
                              >
                                <Text as={"span"} ml={3}>
                                  <MdDragHandle
                                    style={{ cursor: "grab" }}
                                    fontSize={"24px"}
                                  />
                                </Text>
                              </Flex>
                            )}
                          </Td>
                        </Tr>
                      )}
                    </>
                  );
                })
              )}
            </Tbody>
          </Table>
        </TableContainer>
      )}

      {/* Modal for course search */}
      <Modal isOpen={isOpen} onClose={onClose} size="6xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            <Flex justifyContent={"space-between"} alignItems={"center"}>
              <Text mb={0}>Search Coach</Text>
              <Flex>
                {!(!cmsCoachData?.isLoading && cmsCoachData?.error) &&
                  !adjustBtnEdit &&
                  userData?.accessScopes?.cms?.includes("write") && (
                    <Flex
                      justifyContent={"flex-end"}
                      alignItems={"center"}
                      mr={7}
                    >
                      <Button
                        variant={"outline"}
                        colorScheme="red"
                        size={"sm"}
                        py={3}
                        px={4}
                        mr={2}
                        isDisabled={!isUpdated}
                        onClick={() => {
                          getCmsCoachData();
                          setIsUpdated(false);
                          onClose();
                        }}
                      >
                        Discard
                      </Button>
                      <Button
                        variant={"outline"}
                        colorScheme="green"
                        size={"sm"}
                        py={3}
                        px={4}
                        isDisabled={!isUpdated}
                        onClick={onOpen1}
                        isLoading={saveChangesBtnLoading}
                      >
                        Save Changes
                      </Button>
                    </Flex>
                  )}
                <ModalCloseButton
                  onClick={() => {
                    getCmsCoachData();
                    setIsUpdated(false);
                  }}
                />
              </Flex>
            </Flex>
          </ModalHeader>

          <ModalBody>
            <Card mt={3} variant="outline" height={"95%"}>
              <CardBody>
                <Box>
                  <Stack direction="row" spacing={4} align="center">
                    <Input
                      type="text"
                      placeholder="Search Coach"
                      onChange={(e) => setSearchQuery(e.target.value)}
                      onKeyDown={(e) =>
                        e.key === "Enter" && getCoachData(searchQuery)
                      }
                    />
                    <Button
                      variant="solid"
                      colorScheme="telegram"
                      onClick={() => getCoachData(searchQuery)}
                      isDisabled={!(searchQuery.length > 0)}
                    >
                      Search
                    </Button>
                  </Stack>
                  {/* Searched Course List */}
                  {!coachData?.isLoading && coachData?.error ? (
                    <Flex
                      justifyContent={"center"}
                      alignItems={"center"}
                      w={"full"}
                      my={10}
                    >
                      <Text color={"red.500"}>
                        Something went wrong please try again later...
                      </Text>
                    </Flex>
                  ) : coachData.result.length > 0 ? (
                    <TableContainer
                      mt={6}
                      height={`${window.innerHeight - 300}px`}
                      overflowY={"scroll"}
                    >
                      <Table variant="simple">
                        <Thead
                          bgColor={"#c1eaee"}
                          position={"sticky"}
                          top={"0px"}
                          zIndex={"99"}
                        >
                          <Tr bgColor={"#E2DFDF"}>
                            <Th>S.No</Th>
                            <Th>Coach Name</Th>
                            <Th>Categories</Th>
                            <Th>Status</Th>
                            <Th>Auth Status</Th>
                            <Th>Experience</Th>
                            <Th textAlign={"center"}>Action</Th>
                          </Tr>
                        </Thead>
                        <Tbody>
                          {coachData?.isLoading && !coachData?.error ? (
                            <Tr>
                              <Td></Td>
                              <Td> </Td>
                              <Td></Td>
                              <Td
                                display={"flex"}
                                justifyContent={"center"}
                                alignItems={"center"}
                              >
                                <Spinner />
                              </Td>
                              <Td></Td>
                              <Td></Td>
                              <Td></Td>
                            </Tr>
                          ) : (
                            coachData?.result?.map((coachData, inx) => {
                              return (
                                <Tr key={coachData?._id}>
                                  <Td>{inx + 1 + "."}</Td>
                                  <Td
                                    style={{
                                      whiteSpace: "pre-wrap",
                                      wordWrap: "break-word",
                                    }}
                                  >
                                    {(coachData?.firstName || "") +
                                      " " +
                                      (coachData?.lastName || "")}
                                  </Td>
                                  <Td>
                                    {coachData?.sportsCategories?.length > 0 ? (
                                      <UnorderedList>
                                        {coachData?.sportsCategories?.map(
                                          (cat, indx) => (
                                            <ListItem key={indx}>
                                              {cat}
                                            </ListItem>
                                          )
                                        )}
                                      </UnorderedList>
                                    ) : (
                                      "n/a"
                                    )}
                                  </Td>
                                  <Td>
                                    <Badge
                                      colorScheme={
                                        coachData?.status === "active"
                                          ? "green"
                                          : "red"
                                      }
                                    >
                                      {coachData?.status?.toUpperCase()}
                                    </Badge>
                                  </Td>
                                  <Td>
                                    <Badge
                                      colorScheme={
                                        coachData?.authStatus === "authorized"
                                          ? "green"
                                          : "red"
                                      }
                                    >
                                      {coachData?.authStatus?.toUpperCase()}
                                    </Badge>
                                  </Td>
                                  <Td>
                                    {coachData?.experience
                                      ? coachData?.experience + " Years"
                                      : "n/a"}
                                  </Td>
                                  <Td>
                                    {cmsCoachData?.result.every(
                                      (z) => z?.coach?._id !== coachData?._id
                                    ) ? (
                                      <Button
                                        colorScheme="telegram"
                                        isDisabled={
                                          coachData?.status !== "active" ||
                                          coachData?.authStatus !== "authorized"
                                        }
                                        size={"sm"}
                                        onClick={() => {
                                          setIsUpdated(true);
                                          setCmsCoachData((prevState) => ({
                                            ...prevState,
                                            result: [
                                              ...prevState.result,
                                              { coach: coachData },
                                            ],
                                          }));
                                        }}
                                      >
                                        Add
                                      </Button>
                                    ) : (
                                      <Button
                                        colorScheme="red"
                                        size={"sm"}
                                        onClick={() => {
                                          setIsUpdated(true);
                                          setCmsCoachData((prevState) => ({
                                            ...prevState,
                                            result: prevState.result.filter(
                                              (obj) =>
                                                obj?.coach?._id !==
                                                coachData?._id
                                            ),
                                          }));
                                        }}
                                      >
                                        Remove
                                      </Button>
                                    )}
                                  </Td>
                                </Tr>
                              );
                            })
                          )}
                        </Tbody>
                      </Table>
                    </TableContainer>
                  ) : coachData?.isLoading && !coachData?.error ? (
                    <Flex
                      justifyContent={"center"}
                      alignItems={"center"}
                      mt={6}
                    >
                      <Spinner size={"lg"} />
                    </Flex>
                  ) : (
                    <Flex
                      justifyContent={"center"}
                      alignItems={"center"}
                      mt={6}
                    >
                      <Text
                        fontSize={"18px"}
                        fontWeight={"semibold"}
                        color={"red.300"}
                      >
                        Result not found
                      </Text>
                    </Flex>
                  )}
                </Box>
              </CardBody>
            </Card>
          </ModalBody>
        </ModalContent>
      </Modal>
      {/* Save Changes Alert */}
      <AlertDialog
        motionPreset="slideInBottom"
        onClose={onClose1}
        isOpen={isOpen1}
        isCentered
      >
        <AlertDialogOverlay />

        <AlertDialogContent>
          <AlertDialogHeader>Save Changes</AlertDialogHeader>
          <AlertDialogCloseButton />
          <AlertDialogBody>
            Are you sure you want to make these changes.
          </AlertDialogBody>
          <AlertDialogFooter>
            <Button onClick={onClose1}>No</Button>
            <Button
              colorScheme="telegram"
              ml={3}
              onClick={() => updateAndSave()}
            >
              Yes
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Layout>
  );
};

export default TopCoaches;
