import React, { useState, useEffect, useRef } from "react";
import Layout from "../../layout/default";
import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  FormErrorMessage,
  Input,
  Select,
  Stack,
  Heading,
  Avatar,
  IconButton,
  useToast,
  Image,
  Tag,
  TagLabel,
  TagCloseButton,
  Divider,
  Text,
  InputGroup,
  InputRightElement,
  VStack,
  HStack,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Button as ChakraButton,
  Checkbox,
  CheckboxGroup,
  useDisclosure,
  Portal,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Tooltip,
  Textarea,
  Icon
} from "@chakra-ui/react";
import LogoImage from "../../assets/images/mask/MainKhelCoach.svg";
import { FiPlus, FiTrash, FiChevronDown } from "react-icons/fi";
import { IoMdArrowRoundBack } from "react-icons/io";
import { useNavigate, Link } from "react-router-dom";
import { uploadImage } from "../../utilities/uploadImage";
import { State } from "country-state-city";
import { useFormik } from "formik";
import * as Yup from "yup";
import axios from "axios";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";

// Validation schema using Yup
const validationSchema = Yup.object({
  academyName: Yup.string()
    .min(2, "Academy name must be at least 2 characters")
    .max(100, "Academy name cannot exceed 100 characters")
    .required("Academy name is required"),
  email: Yup.string()
    .email("Please enter a valid email address")
    .required("Email is required"),
  password: Yup.string()
    .min(8, "Password must be at least 8 characters")
    .required("Password is required"),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('password'), null], 'Passwords must match')
    .required('Confirm password is required'),
  phone: Yup.string()
    .matches(/^\d{10}$/, "Phone number must be exactly 10 digits")
    .required("Phone number is required"),
  companyRegNo: Yup.string()
    .required("Company registration number is required"),
  // Office Address Fields
  officeAddress: Yup.string()
    .required("Office address line 1 is required"),
  officeAddressLine2: Yup.string(),
  city: Yup.string()
    .required("City is required"),
  state: Yup.string()
    .required("State is required"),
  pinCode: Yup.string()
    .matches(/^\d{6}$/, "Pin code must be exactly 6 digits")
    .required("Pin code is required"),
  country: Yup.string()
    .required("Country is required"),
  // Facility Addresses
  facilityAddresses: Yup.array().of(
    Yup.object({
      name: Yup.string()
        .required("Facility name is required"),
      addressLine1: Yup.string()
        .required("Facility address line 1 is required"),
      addressLine2: Yup.string(),
      city: Yup.string()
        .required("City is required"),
      state: Yup.string()
        .required("State is required"),
      pinCode: Yup.string()
        .matches(/^\d{6}$/, "Pin code must be exactly 6 digits")
        .required("Pin code is required"),
      country: Yup.string()
        .required("Country is required"),
      amenities: Yup.string()
    })
  ),
  // Document Fields
  panNumber: Yup.string()
    .matches(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, "PAN number must be in format **********")
    .required("PAN number is required"),
  aadhaarNo: Yup.string()
    .matches(/^\d{12}$/, "Aadhaar number must be exactly 12 digits")
    .required("Aadhaar number is required"),
  // Bank Details - Now Required
  accountNo: Yup.string()
    .min(9, "Account number must be at least 9 digits")
    .max(18, "Account number cannot exceed 18 digits")
    .matches(/^\d+$/, "Account number must contain only digits")
    .required("Account number is required"),
  ifscCode: Yup.string()
    .matches(/^[A-Z]{4}0[A-Z0-9]{6}$/, "IFSC code must be in format ABCD0123456")
    .required("IFSC code is required"),
  gstNumber: Yup.string()
    .matches(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/, "GST number must be in valid format")
    .required("GST number is required"),
  platformFee: Yup.string()
    .required("Platform fee is required")
});

const AcademyCreation = () => {
  const [profileImage, setProfileImage] = useState(null);
  const [panFile, setPanFile] = useState(null);
  const [aadhaarFile, setAadhaarFile] = useState(null);
  const [categories, setCategories] = useState([]);
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [categoryDropdownOpen, setCategoryDropdownOpen] = useState(false);
  const [states, setStates] = useState([]);
  const [pincodeError, setPincodeError] = useState(false);
  const [facilityPincodeErrors, setFacilityPincodeErrors] = useState({});
  const [pincodeLoading, setPincodeLoading] = useState(false);
  const [facilityPincodeLoading, setFacilityPincodeLoading] = useState({});
  const [facilityErrors, setFacilityErrors] = useState({}); // Track facility-specific errors
  const [formErrors, setFormErrors] = useState({}); // Track form field errors
  const toast = useToast();
  const navigate = useNavigate();
  const categoryInputRef = useRef();
  const [showPassword, setShowPassword] = useState(false);

  // Formik form management
  const formik = useFormik({
    initialValues: {
      academyName: "",
      email: "",
      password: "",
      confirmPassword: "",
      phone: "",
      companyRegNo: "",
      officeAddress: "",
      officeAddressLine2: "",
      city: "",
      state: "",
      pinCode: "",
      country: "",
      facilityAddresses: [{
        name: "",
        addressLine1: "",
        addressLine2: "",
        city: "",
        state: "",
        pinCode: "",
        country: "",
        amenities: ""
      }],
      panNumber: "",
      aadhaarNo: "",
      accountNo: "",
      ifscCode: "",
      gstNumber: "",
      platformFee: "",
    },
    validationSchema: validationSchema,
    onSubmit: (values) => {
      handleRegister(values);
    }
  });

  useEffect(() => {
    const stateData = State.getStatesOfCountry("IN");
    setStates(stateData);
  }, []);

  useEffect(() => {
    axios.get(`${process.env.REACT_APP_BASE_URL}/api/category?page=1`)
      .then(res => {
        setCategories(res.data.data || []);
      })
      .catch(err => {
        toast({ title: "Failed to fetch categories", status: "error" });
      });
  }, [toast]);

  // Handle click outside for category dropdown
  useEffect(() => {
    function handleClickOutside(event) {
      if (categoryInputRef.current && !categoryInputRef.current.contains(event.target)) {
        setCategoryDropdownOpen(false);
      }
    }
    if (categoryDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [categoryDropdownOpen]);

  // Pincode validation function for office address
  const getDetailsFromPincode = async (pincode) => {
    try {
      if (!pincode || pincode.length !== 6) {
        setPincodeError(true);
        return;
      }
      
      setPincodeLoading(true);
      setPincodeError(false);
      
      const details = await axios.get(
        `https://api.postalpincode.in/pincode/${pincode}`,
        { timeout: 5000 }
      );
      
      setPincodeLoading(false);
      
      if (
        details?.data[0]?.Status === "Error" ||
        details?.data[0]?.Status === "404" ||
        details?.data[0]?.Message === "No records found"
      ) {
        setPincodeError(true);
        formik.setFieldValue('city', '');
        formik.setFieldValue('state', '');
        toast({
          title: "Invalid Pincode",
          description: "The pincode you entered is not valid. Please check and try again.",
          status: "error",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
        return;
      } else {
        setPincodeError(false);
        const postOffice = details?.data[0]?.PostOffice[0];
        const stateData = states.find(state => state.name === postOffice?.State);
        
        formik.setFieldValue('city', `${postOffice?.Name}, ${postOffice?.District}`);
        formik.setFieldValue('state', stateData?.name || postOffice?.State);
        formik.setFieldValue('country', 'India');
        
        toast({
          title: "Location Found",
          description: `Auto-filled city and state for pincode ${pincode}`,
          status: "success",
          duration: 2000,
          position: "top",
          isClosable: true,
        });
      }
    } catch (error) {
      setPincodeLoading(false);
      console.log("Pincode lookup error:", error);
      setPincodeError(true);
      toast({
        title: "Error validating pincode",
        description: "Unable to validate pincode. Please check your internet connection and try again.",
        status: "error",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
    }
  };

  // Pincode validation function for facility addresses
  const getDetailsFromPincodeForFacility = async (pincode, facilityIndex) => {
    try {
      if (!pincode || pincode.length !== 6) {
        setFacilityPincodeErrors(prev => ({ ...prev, [facilityIndex]: true }));
        return;
      }
      
      setFacilityPincodeLoading(prev => ({ ...prev, [facilityIndex]: true }));
      setFacilityPincodeErrors(prev => ({ ...prev, [facilityIndex]: false }));
      
      const details = await axios.get(
        `https://api.postalpincode.in/pincode/${pincode}`,
        { timeout: 5000 }
      );
      
      setFacilityPincodeLoading(prev => ({ ...prev, [facilityIndex]: false }));
      
      if (
        details?.data[0]?.Status === "Error" ||
        details?.data[0]?.Status === "404" ||
        details?.data[0]?.Message === "No records found"
      ) {
        setFacilityPincodeErrors(prev => ({ ...prev, [facilityIndex]: true }));
        
        // Clear city and state for this facility
        const updatedFacilities = [...formik.values.facilityAddresses];
        updatedFacilities[facilityIndex] = {
          ...updatedFacilities[facilityIndex],
          pinCode: pincode, // Explicitly preserve the correct pincode
          city: "",
          state: ""
        };
        formik.setFieldValue('facilityAddresses', updatedFacilities);
        
        toast({
          title: "Invalid Pincode",
          description: `The pincode you entered for facility ${facilityIndex + 1} is not valid. Please check and try again.`,
          status: "error",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
        return;
      } else {
        setFacilityPincodeErrors(prev => ({ ...prev, [facilityIndex]: false }));
        const postOffice = details?.data[0]?.PostOffice[0];
        const stateData = states.find(state => state.name === postOffice?.State);
        
        // Update city and state for this facility
        const updatedFacilities = [...formik.values.facilityAddresses];
        updatedFacilities[facilityIndex] = {
          ...updatedFacilities[facilityIndex],
          pinCode: pincode, // Explicitly preserve the correct pincode
          city: `${postOffice?.Name}, ${postOffice?.District}`,
          state: stateData?.name || postOffice?.State,
          country: "India"
        };
        formik.setFieldValue('facilityAddresses', updatedFacilities);
        
        toast({
          title: "Location Found",
          description: `Auto-filled city and state for facility ${facilityIndex + 1} pincode ${pincode}`,
          status: "success",
          duration: 2000,
          position: "top",
          isClosable: true,
        });
      }
    } catch (error) {
      setFacilityPincodeLoading(prev => ({ ...prev, [facilityIndex]: false }));
      console.log("Pincode lookup error:", error);
      setFacilityPincodeErrors(prev => ({ ...prev, [facilityIndex]: true }));
      toast({
        title: "Error validating facility pincode",
        description: `Unable to validate pincode for facility ${facilityIndex + 1}. Please check your internet connection and try again.`,
        status: "error",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
    }
  };

  const handleRegister = async (values) => {
    // Custom validation: Check if Aadhaar number and image are both provided
    if ((values.aadhaarNo?.trim() && !formik.values.aadhaarCard) || (!values.aadhaarNo?.trim() && formik.values.aadhaarCard)) {
      formik.setSubmitting(false);
      toast({
        title: "Validation Error",
        description: "Both Aadhaar Number and Aadhaar Image must be provided together.",
        status: "error",
        duration: 5000,
        position: "top",
        isClosable: true,
      });
      return;
    }

    try {
      // Upload profile image if present
      let profileImageUrl = null;
      if (profileImage) {
        const res = await uploadImage(profileImage);
        profileImageUrl = res.url || res;
      }
      
      // Upload PAN image if present
      let panImageUrl = null;
      if (formik.values.panCard) {
        const res = await uploadImage(formik.values.panCard);
        panImageUrl = res.url || res;
      }

      // Upload Aadhaar image if present
      let aadhaarImageUrl = null;
      if (formik.values.aadhaarCard) {
        const res = await uploadImage(formik.values.aadhaarCard);
        aadhaarImageUrl = res.url || res;
      }

      // Map selectedCategories (IDs) to names
      const sportsCategories = selectedCategories
        .map(cid => categories.find(cat => cat._id === cid)?.name)
        .filter(Boolean);

      // Prepare office address with all required fields
      const officeAddress = {
        addressLine1: values.officeAddress,
        city: values.city,
        state: values.state,
        pinCode: values.pinCode,
        country: values.country
      };
      
      // Add optional office address fields only if they have values
      if (values.officeAddressLine2?.trim()) {
        officeAddress.addressLine2 = values.officeAddressLine2;
      }

      // Prepare linkedFacilities with all required fields
      const linkedFacilities = values.facilityAddresses.map(addr => {
        const facility = {
          name: addr.name,
          addressLine1: addr.addressLine1,
          city: addr.city,
          state: addr.state,
          pinCode: addr.pinCode,
          country: addr.country,
          location: {
            type: "Point",
            coordinates: [77.5946, 12.9716], // Example coordinates
            is_location_exact: true
          }
        };
        
        // Add optional facility fields only if they have values
        if (addr.addressLine2?.trim()) {
          facility.addressLine2 = addr.addressLine2;
        }
        if (addr.amenities?.trim()) {
          facility.amenities = addr.amenities;
        }
        
        return facility;
      });

      // Prepare bank details - only include if account details are provided
      let bankDetails = null;
      if (values.accountNo?.trim() || values.ifscCode?.trim()) {
        bankDetails = {
          accountHolderName: values.academyName
        };
        
        if (values.accountNo?.trim()) {
          bankDetails.accountNumber = values.accountNo;
        }
        if (values.ifscCode?.trim()) {
          bankDetails.ifsc = values.ifscCode;
        }
      }

      // Prepare payload with all required fields
      const payload = {
        name: values.academyName,
        mobile: values.phone,
        email: values.email,
        gstNumber: values.gstNumber,
        platformFee: values.platformFee,
        sportsCategories,
        officeAddress,
        companyRegistrationNumber: values.companyRegNo,
        linkedFacilities,
        password: values.password,
        panNumber: values.panNumber,
        aadhaarNumber: values.aadhaarNo
      };

      // Add optional fields only if they have values
      if (profileImageUrl) {
        payload.profileImage = profileImageUrl;
      }
      
      if (panImageUrl) {
        payload.panImage = panImageUrl;
      }
      
      // For Aadhaar, include image if we have both number and image
      if (values.aadhaarNo?.trim() && aadhaarImageUrl) {
        payload.aadhaarImage = aadhaarImageUrl;
      }
      
      if (bankDetails) {
        payload.bankDetails = bankDetails;
      }


      // Get Bearer token from sessionStorage
      const token = sessionStorage.getItem("admintoken");

      // Make API call
      await axios.post(`${process.env.REACT_APP_BASE_URL}/api/academy`, payload, {
        headers: {
          Authorization: token,
          "Content-Type": "application/json"
        }
      });

      toast({ title: "Registration successful!", status: "success" });
      navigate("/academy-page");
    } catch (err) {
      console.log("Academy Registration API Error:", err);
      
      // Reset form submitting state
      formik.setSubmitting(false);
      
      // Enhanced error handling with field highlighting
      let errorMessage = "Registration failed";
      let errorTitle = "Registration Failed";
      
      if (err.response) {
        // Server responded with error status
        const responseData = err.response.data;
        
        // Handle backend validation errors with field highlighting
        if (responseData?.errors && Array.isArray(responseData.errors)) {
          errorTitle = "Validation Error";
          
          // Set field errors for highlighting
          const fieldErrors = {};
          const facilityFieldErrors = {};
          const errorMessages = [];
          
          responseData.errors.forEach(errorItem => {
            // Handle errors with message (field can be null for general validation errors)
            if (errorItem.message) {
              // Handle errors with specific fields
              if (errorItem.field) {
                // Handle main form field errors
                if (errorItem.field.startsWith('officeAddress.')) {
                  // Map office address fields
                  const fieldName = errorItem.field.replace('officeAddress.', '');
                  if (fieldName === 'addressLine1') {
                    fieldErrors['officeAddress'] = errorItem.message;
                  } else if (fieldName === 'addressLine2') {
                    fieldErrors['officeAddressLine2'] = errorItem.message;
                  } else {
                    fieldErrors[fieldName] = errorItem.message;
                  }
                  errorMessages.push(`Office Address - ${fieldName}: ${errorItem.message}`);
                } 
                // Handle facility field errors
                else if (errorItem.field.startsWith('linkedFacilities.')) {
                  // Extract facility index and field name
                  const facilityMatch = errorItem.field.match(/linkedFacilities\.(\d+)\.(.+)/);
                  if (facilityMatch) {
                    const facilityIndex = parseInt(facilityMatch[1]);
                    const facilityFieldName = facilityMatch[2];
                    
                    // Store facility errors by index and field
                    if (!facilityFieldErrors[facilityIndex]) {
                      facilityFieldErrors[facilityIndex] = {};
                    }
                    facilityFieldErrors[facilityIndex][facilityFieldName] = errorItem.message;
                    
                    errorMessages.push(`Facility ${facilityIndex + 1} - ${facilityFieldName}: ${errorItem.message}`);
                  } else {
                    errorMessages.push(`${errorItem.field}: ${errorItem.message}`);
                  }
                }
                // Handle other form field errors
                else {
                  // Map common field names
                  let formFieldName = errorItem.field;
                  if (errorItem.field === 'mobile') {
                    formFieldName = 'phone';
                  } else if (errorItem.field === 'name') {
                    formFieldName = 'academyName';
                  } else if (errorItem.field === 'companyRegistrationNumber') {
                    formFieldName = 'companyRegNo';
                  } else if (errorItem.field === 'accountNumber') {
                    formFieldName = 'accountNo';
                  } else if (errorItem.field === 'panNumber') {
                    formFieldName = 'panNumber';
                  } else if (errorItem.field === 'aadhaarNumber') {
                    formFieldName = 'aadhaarNo';
                  }
                  
                  fieldErrors[formFieldName] = errorItem.message;
                  errorMessages.push(`${errorItem.field}: ${errorItem.message}`);
                }
              } 
              // Handle general validation errors (field is null)
              else {
                // These are general validation errors that don't map to specific fields
                errorMessages.push(errorItem.message);
              }
            }
          });
          
          // Set form field errors for highlighting
          if (Object.keys(fieldErrors).length > 0) {
            // Form submission handled by formik
            }          // Set facility errors for highlighting
          if (Object.keys(facilityFieldErrors).length > 0) {
            setFacilityErrors(facilityFieldErrors);
          }
          
          errorMessage = errorMessages.join(", ");
        } else if (responseData?.details && Array.isArray(responseData.details)) {
          errorTitle = "Validation Error";
          errorMessage = responseData.details.map(detail => detail.message).join(", ");
        } else if (responseData?.message) {
          errorMessage = responseData.message;
        } else {
          errorMessage = `Server error: ${err.response.status}`;
        }
      } else if (err.request) {
        // Request was made but no response received
        errorMessage = "Network error: Unable to connect to server";
      }
      
      toast({
        title: errorTitle,
        description: errorMessage,
        status: "error",
        duration: 5000,
        position: "top",
        isClosable: true,
      });
    }
  };

  const handleFacilityAddressChange = (index, value) => {
    const updated = [...formik.values.facilityAddresses];
    updated[index] = value;
    formik.setFieldValue('facilityAddresses', updated);
  };

  const addFacilityAddress = () => {
    const newFacility = {
      name: "",
      addressLine1: "",
      addressLine2: "",
      city: "",
      state: "",
      pinCode: "",
      country: "",
      amenities: ""
    };
    formik.setFieldValue('facilityAddresses', [...formik.values.facilityAddresses, newFacility]);
  };

  const removeFacilityAddress = (index) => {
    const updated = formik.values.facilityAddresses.filter((_, i) => i !== index);
    // Also remove any pincode errors for this facility
    const updatedErrors = { ...facilityPincodeErrors };
    delete updatedErrors[index];
    // Adjust error keys for remaining facilities
    const adjustedErrors = {};
    Object.keys(updatedErrors).forEach(key => {
      const keyIndex = parseInt(key);
      if (keyIndex > index) {
        adjustedErrors[keyIndex - 1] = updatedErrors[key];
      } else {
        adjustedErrors[key] = updatedErrors[key];
      }
    });
    setFacilityPincodeErrors(adjustedErrors);
    formik.setFieldValue('facilityAddresses', updated);
  };

  // Image handlers
  const handleProfileImage = e => {
    if (e.target.files[0]) setProfileImage(e.target.files[0]);
  };
  const removeProfileImage = () => setProfileImage(null);

  const handlePanFile = e => {
    if (e.target.files[0]) setPanFile(e.target.files[0]);
  };
  const removePanFile = () => setPanFile(null);

  const handleAadhaarFile = e => {
    if (e.target.files[0]) setAadhaarFile(e.target.files[0]);
  };
  const removeAadhaarFile = () => setAadhaarFile(null);

  // Sports categories handlers
  const handleCategorySelect = id => {
    setSelectedCategories(prev =>
      prev.includes(id) ? prev.filter(cid => cid !== id) : [...prev, id]
    );
  };
  const removeCategory = id => {
    setSelectedCategories(selectedCategories.filter(cid => cid !== id));
  };

  return (
    <Box bgColor={"#f2f2f2"} width={"100%"}>
      <form onSubmit={formik.handleSubmit}>
      <Layout title="Create Academy" content="container">
        {/* Breadcrumb */}
        <Flex justifyContent={"space-between"} alignItems={"center"} mb={6}>
          <Flex justifyContent={"center"} alignItems={"center"}>
            <Tooltip label="Back">
              <Text
                mr={3}
                as={"span"}
                fontSize={"28px"}
                cursor={"pointer"}
                onClick={() => navigate(-1)}
              >
                <IoMdArrowRoundBack />
              </Text>
            </Tooltip>
            <Breadcrumb fontWeight="medium" fontSize="sm">
              <BreadcrumbItem>
                <Link to={"/"}>Dashboard</Link>
              </BreadcrumbItem>
              <BreadcrumbItem>
                <Link to="/academy-page">Academy</Link>
              </BreadcrumbItem>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">Create Academy</BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>
          </Flex>
        </Flex>

        {/* Main Content */}
        <Box bg="white" p={{ base: 4, md: 8 }} rounded="lg" boxShadow="lg" width="100%">
          {/* <Heading mb={2} textAlign="center" fontSize="2xl">Academy Registration</Heading>
          <Text mb={6} color="gray.500" textAlign="center">Fill in the details to register your academy</Text> */}
          <Stack spacing={6} divider={<Divider />}> 
              {/* Profile Section */}
              <Flex gap={8} align="flex-start" direction={{ base: "column", md: "row" }}>
                <Box minW="160px">
                  <FormLabel fontWeight="bold" mb={3}>
                    Profile Image <Text as="span" color="red.500">*</Text>
                  </FormLabel>
                  <VStack spacing={3} align="center">
                    <Box position="relative">
                      <Avatar 
                        size="xl" 
                        src={profileImage ? URL.createObjectURL(profileImage) : undefined} 
                        bg={profileImage ? undefined : "gray.100"}
                        border="3px solid"
                        borderColor={profileImage ? "green.200" : "gray.200"}
                        transition="all 0.2s"
                      />
                      {profileImage && (
                        <Box
                          position="absolute"
                          top={0}
                          right={0}
                          bg="white"
                          borderRadius="full"
                          p={1}
                          boxShadow="md"
                          border="2px solid"
                          borderColor="gray.100"
                        >
                          <Icon 
                            as={FiTrash} 
                            boxSize={4} 
                            color="red.500"
                            cursor="pointer"
                            onClick={removeProfileImage}
                            _hover={{ color: "red.600" }}
                          />
                        </Box>
                      )}
                    </Box>
                    <VStack spacing={2} w="full">
                      <Input type="file" accept="image/*" display="none" id="profile-image-upload" onChange={handleProfileImage} />
                      <Button 
                        as="label" 
                        htmlFor="profile-image-upload" 
                        leftIcon={<FiPlus />}
                        size="sm"
                        colorScheme={profileImage ? "blue" : "teal"}
                        variant={profileImage ? "outline" : "solid"}
                        w="full"
                      >
                        {profileImage ? "Change Photo" : "Upload Photo"}
                      </Button>
                      {profileImage && (
                        <Button 
                          colorScheme="red" 
                          leftIcon={<FiTrash />} 
                          onClick={removeProfileImage} 
                          variant="outline"
                          size="sm"
                          w="full"
                        >
                          Remove Photo
                        </Button>
                      )}
                    </VStack>
                  </VStack>
                </Box>
                <Stack flex={1} spacing={4}>
                  <FormControl isInvalid={formik.errors.academyName && formik.touched.academyName}>
                    <FormLabel fontWeight="bold">
                      Academy Name <Text as="span" color="red.500">*</Text>
                    </FormLabel>
                    <Input 
                      name="academyName"
                      value={formik.values.academyName} 
                      placeholder="Enter academy name *" 
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      borderColor={
                        (formik.errors.academyName && formik.touched.academyName) || formErrors.academyName 
                          ? "red.300" 
                          : "gray.200"
                      }
                      _hover={{
                        borderColor: (formik.errors.academyName && formik.touched.academyName) || formErrors.academyName 
                          ? "red.400" 
                          : "gray.300"
                      }}
                      _focus={{
                        borderColor: (formik.errors.academyName && formik.touched.academyName) || formErrors.academyName 
                          ? "red.500" 
                          : "blue.500",
                        shadow: (formik.errors.academyName && formik.touched.academyName) || formErrors.academyName 
                          ? "0 0 0 1px var(--chakra-colors-red-500)" 
                          : "0 0 0 1px var(--chakra-colors-blue-500)"
                      }}
                    />
                    <FormErrorMessage>{formik.errors.academyName}</FormErrorMessage>
                    {formErrors.academyName && !formik.errors.academyName && (
                      <Text color="red.500" fontSize="sm" mt={1}>
                        {formErrors.academyName}
                      </Text>
                    )}
                  </FormControl>
                  <FormControl isInvalid={formik.errors.email && formik.touched.email}>
                    <FormLabel fontWeight="bold">
                      Email <Text as="span" color="red.500">*</Text>
                    </FormLabel>
                    <Input 
                      name="email"
                      type="email"
                      value={formik.values.email} 
                      placeholder="Email address *" 
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      borderColor={
                        (formik.errors.email && formik.touched.email) || formErrors.email 
                          ? "red.300" 
                          : "gray.200"
                      }
                      _hover={{
                        borderColor: (formik.errors.email && formik.touched.email) || formErrors.email 
                          ? "red.400" 
                          : "gray.300"
                      }}
                      _focus={{
                        borderColor: (formik.errors.email && formik.touched.email) || formErrors.email 
                          ? "red.500" 
                          : "blue.500",
                        shadow: (formik.errors.email && formik.touched.email) || formErrors.email 
                          ? "0 0 0 1px var(--chakra-colors-red-500)" 
                          : "0 0 0 1px var(--chakra-colors-blue-500)"
                      }}
                    />
                    <FormErrorMessage>{formik.errors.email}</FormErrorMessage>
                    {formErrors.email && !formik.errors.email && (
                      <Text color="red.500" fontSize="sm" mt={1}>
                        {formErrors.email}
                      </Text>
                    )}
                  </FormControl>
                </Stack>
              </Flex>

              {/* Account Details */}
              <HStack spacing={4} align="flex-start">
                <FormControl isInvalid={formik.errors.password && formik.touched.password} flex={1}>
                  <FormLabel fontWeight="bold">
                    Password <Text as="span" color="red.500">*</Text>
                  </FormLabel>
                  <InputGroup>
                    <Input
                      name="password"
                      type={showPassword ? "text" : "password"}
                      value={formik.values.password}
                      placeholder="Password *"
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      borderColor={
                        (formik.errors.password && formik.touched.password) || formErrors.password 
                          ? "red.300" 
                          : "gray.200"
                      }
                      _hover={{
                        borderColor: (formik.errors.password && formik.touched.password) || formErrors.password 
                          ? "red.400" 
                          : "gray.300"
                      }}
                      _focus={{
                        borderColor: (formik.errors.password && formik.touched.password) || formErrors.password 
                          ? "red.500" 
                          : "blue.500",
                        shadow: (formik.errors.password && formik.touched.password) || formErrors.password 
                          ? "0 0 0 1px var(--chakra-colors-red-500)" 
                          : "0 0 0 1px var(--chakra-colors-blue-500)"
                      }}
                    />
                    <InputRightElement width="4.5rem">
                      <Button h="1.75rem" size="sm" onClick={() => setShowPassword(!showPassword)}>
                        {showPassword ? "Hide" : "Show"}
                      </Button>
                    </InputRightElement>
                  </InputGroup>
                  <FormErrorMessage>{formik.errors.password}</FormErrorMessage>
                  {formErrors.password && !formik.errors.password && (
                    <Text color="red.500" fontSize="sm" mt={1}>
                      {formErrors.password}
                    </Text>
                  )}
                </FormControl>
                <FormControl isInvalid={formik.errors.confirmPassword && formik.touched.confirmPassword} flex={1}>
                  <FormLabel fontWeight="bold">
                    Confirm Password <Text as="span" color="red.500">*</Text>
                  </FormLabel>
                  <InputGroup>
                    <Input
                      name="confirmPassword"
                      type={showPassword ? "text" : "password"}
                      value={formik.values.confirmPassword}
                      placeholder="Confirm Password *"
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      borderColor={
                        (formik.errors.confirmPassword && formik.touched.confirmPassword) || formErrors.confirmPassword 
                          ? "red.300" 
                          : "gray.200"
                      }
                      _hover={{
                        borderColor: (formik.errors.confirmPassword && formik.touched.confirmPassword) || formErrors.confirmPassword 
                          ? "red.400" 
                          : "gray.300"
                      }}
                      _focus={{
                        borderColor: (formik.errors.confirmPassword && formik.touched.confirmPassword) || formErrors.confirmPassword 
                          ? "red.500" 
                          : "blue.500",
                        shadow: (formik.errors.confirmPassword && formik.touched.confirmPassword) || formErrors.confirmPassword 
                          ? "0 0 0 1px var(--chakra-colors-red-500)" 
                          : "0 0 0 1px var(--chakra-colors-blue-500)"
                      }}
                    />
                  </InputGroup>
                  <FormErrorMessage>{formik.errors.confirmPassword}</FormErrorMessage>
                  {formErrors.confirmPassword && !formik.errors.confirmPassword && (
                    <Text color="red.500" fontSize="sm" mt={1}>
                      {formErrors.confirmPassword}
                    </Text>
                  )}
                </FormControl>
              </HStack>

              {/* Phone and Registration Number on next line */}
              <HStack spacing={4} align="flex-start">
                <FormControl isInvalid={formik.errors.phone && formik.touched.phone} flex={1}>
                  <FormLabel fontWeight="bold">
                    Phone Number <Text as="span" color="red.500">*</Text>
                  </FormLabel>
                  <Input 
                    name="phone"
                    value={formik.values.phone} 
                    placeholder="Phone Number *" 
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    borderColor={
                      (formik.errors.phone && formik.touched.phone) || formErrors.phone 
                        ? "red.300" 
                        : "gray.200"
                    }
                    _hover={{
                      borderColor: (formik.errors.phone && formik.touched.phone) || formErrors.phone 
                        ? "red.400" 
                        : "gray.300"
                    }}
                    _focus={{
                      borderColor: (formik.errors.phone && formik.touched.phone) || formErrors.phone 
                        ? "red.500" 
                        : "blue.500",
                      shadow: (formik.errors.phone && formik.touched.phone) || formErrors.phone 
                        ? "0 0 0 1px var(--chakra-colors-red-500)" 
                        : "0 0 0 1px var(--chakra-colors-blue-500)"
                    }}
                  />
                  <FormErrorMessage>{formik.errors.phone}</FormErrorMessage>
                  {formErrors.phone && !formik.errors.phone && (
                    <Text color="red.500" fontSize="sm" mt={1}>
                      {formErrors.phone}
                    </Text>
                  )}
                </FormControl>
                <FormControl isInvalid={formik.errors.companyRegNo && formik.touched.companyRegNo} flex={1}>
                  <FormLabel fontWeight="bold">
                    Company Registration Number <Text as="span" color="red.500">*</Text>
                  </FormLabel>
                  <Input 
                    name="companyRegNo"
                    value={formik.values.companyRegNo} 
                    placeholder="Enter company registration number *" 
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    borderColor={
                      (formik.errors.companyRegNo && formik.touched.companyRegNo) || formErrors.companyRegNo 
                        ? "red.300" 
                        : "gray.200"
                    }
                    _hover={{
                      borderColor: (formik.errors.companyRegNo && formik.touched.companyRegNo) || formErrors.companyRegNo 
                        ? "red.400" 
                        : "gray.300"
                    }}
                    _focus={{
                      borderColor: (formik.errors.companyRegNo && formik.touched.companyRegNo) || formErrors.companyRegNo 
                        ? "red.500" 
                        : "blue.500",
                      shadow: (formik.errors.companyRegNo && formik.touched.companyRegNo) || formErrors.companyRegNo 
                        ? "0 0 0 1px var(--chakra-colors-red-500)" 
                        : "0 0 0 1px var(--chakra-colors-blue-500)"
                    }}
                  />
                  <FormErrorMessage>{formik.errors.companyRegNo}</FormErrorMessage>
                  {formErrors.companyRegNo && !formik.errors.companyRegNo && (
                    <Text color="red.500" fontSize="sm" mt={1}>
                      {formErrors.companyRegNo}
                    </Text>
                  )}
                </FormControl>
              </HStack>

              {/* Office Address Section */}
              <Box>
                <FormLabel fontWeight="bold">
                  Office Address <Text as="span" color="red.500">*</Text>
                </FormLabel>
                <VStack align="stretch" spacing={2}>
                  {/* Pin Code - First for auto-population */}
                  <Box>
                    <Input 
                      name="pinCode"
                      value={formik.values.pinCode} 
                      placeholder="Enter Pin Code to auto-populate location details *" 
                      onChange={e => {
                        const newValue = e.target.value;
                        // Only allow numeric input and limit to reasonable length
                        if (/^\d*$/.test(newValue) && newValue.length <= 8) {
                          formik.setFieldValue('pinCode', newValue);
                          
                          // Trigger validation when user types exactly 6 digits
                          if (newValue.length === 6) {
                            getDetailsFromPincode(newValue);
                          } else if (newValue.length !== 6) {
                            // Reset error state if pincode is not 6 digits
                            setPincodeError(false);
                          }
                        }
                      }}
                      onBlur={e => {
                        const pincode = e.target.value.trim();
                        formik.handleBlur(e);
                        if (pincode && pincode.length === 6) {
                          getDetailsFromPincode(pincode);
                        }
                      }}
                      borderColor={
                        pincodeError || formErrors.pinCode || (formik.errors.pinCode && formik.touched.pinCode)
                          ? "red.300" 
                          : pincodeLoading 
                            ? "blue.500" 
                            : "gray.200"
                      }
                      _hover={{
                        borderColor: pincodeError || formErrors.pinCode || (formik.errors.pinCode && formik.touched.pinCode)
                          ? "red.400" 
                          : pincodeLoading 
                            ? "blue.600" 
                            : "gray.300"
                      }}
                      _focus={{
                        borderColor: pincodeError || formErrors.pinCode || (formik.errors.pinCode && formik.touched.pinCode)
                          ? "red.500" 
                          : pincodeLoading 
                            ? "blue.500" 
                            : "blue.500",
                        shadow: pincodeError || formErrors.pinCode || (formik.errors.pinCode && formik.touched.pinCode)
                          ? "0 0 0 1px var(--chakra-colors-red-500)" 
                          : "0 0 0 1px var(--chakra-colors-blue-500)"
                      }}
                      bg={pincodeLoading ? "blue.50" : undefined}
                      _placeholder={{
                        color: pincodeLoading ? "blue.400" : undefined
                      }}
                      readOnly={pincodeLoading}
                    />
                    {pincodeLoading && (
                      <Text color="blue.500" fontSize="sm" mt={1}>
                        🔍 Looking up location...
                      </Text>
                    )}
                    {(pincodeError || formErrors.pinCode || (formik.errors.pinCode && formik.touched.pinCode)) && !pincodeLoading && (
                      <Text color="red.500" fontSize="sm" mt={1}>
                        {formik.errors.pinCode || formErrors.pinCode || "Invalid pincode - please check and try again"}
                      </Text>
                    )}
                  </Box>
                  {/* City, State, Country Row */}
                  <HStack spacing={2} align="flex-start">
                    <Box flex={1}>
                      <Input 
                        name="city"
                        value={formik.values.city} 
                        placeholder="City *" 
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        borderColor={
                          (formik.errors.city && formik.touched.city) || formErrors.city 
                            ? "red.300" 
                            : "gray.200"
                        }
                        _hover={{
                          borderColor: (formik.errors.city && formik.touched.city) || formErrors.city 
                            ? "red.400" 
                            : "gray.300"
                        }}
                        _focus={{
                          borderColor: (formik.errors.city && formik.touched.city) || formErrors.city 
                            ? "red.500" 
                            : "blue.500",
                          shadow: (formik.errors.city && formik.touched.city) || formErrors.city 
                            ? "0 0 0 1px var(--chakra-colors-red-500)" 
                            : "0 0 0 1px var(--chakra-colors-blue-500)"
                        }}
                      />
                      {((formik.errors.city && formik.touched.city) || formErrors.city) && (
                        <Text color="red.500" fontSize="sm" mt={1}>
                          {formik.errors.city || formErrors.city}
                        </Text>
                      )}
                    </Box>
                    <Box flex={1}>
                      <Select 
                        name="state"
                        value={formik.values.state} 
                        placeholder="Select State *" 
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        borderColor={
                          (formik.errors.state && formik.touched.state) || formErrors.state 
                            ? "red.300" 
                            : "gray.200"
                        }
                        _hover={{
                          borderColor: (formik.errors.state && formik.touched.state) || formErrors.state 
                            ? "red.400" 
                            : "gray.300"
                        }}
                        _focus={{
                          borderColor: (formik.errors.state && formik.touched.state) || formErrors.state 
                            ? "red.500" 
                            : "blue.500",
                          shadow: (formik.errors.state && formik.touched.state) || formErrors.state 
                            ? "0 0 0 1px var(--chakra-colors-red-500)" 
                            : "0 0 0 1px var(--chakra-colors-blue-500)"
                        }}
                      >
                        {states.map(state => (
                          <option key={state.isoCode} value={state.name}>
                            {state.name}
                          </option>
                        ))}
                      </Select>
                      {((formik.errors.state && formik.touched.state) || formErrors.state) && (
                        <Text color="red.500" fontSize="sm" mt={1}>
                          {formik.errors.state || formErrors.state}
                        </Text>
                      )}
                    </Box>
                    <Box flex={1}>
                      <Input 
                        name="country"
                        value={formik.values.country} 
                        placeholder="Country *" 
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        borderColor={
                          (formik.errors.country && formik.touched.country) || formErrors.country 
                            ? "red.300" 
                            : "gray.200"
                        }
                        _hover={{
                          borderColor: (formik.errors.country && formik.touched.country) || formErrors.country 
                            ? "red.400" 
                            : "gray.300"
                        }}
                        _focus={{
                          borderColor: (formik.errors.country && formik.touched.country) || formErrors.country 
                            ? "red.500" 
                            : "blue.500",
                          shadow: (formik.errors.country && formik.touched.country) || formErrors.country 
                            ? "0 0 0 1px var(--chakra-colors-red-500)" 
                            : "0 0 0 1px var(--chakra-colors-blue-500)"
                        }}
                      />
                      {((formik.errors.country && formik.touched.country) || formErrors.country) && (
                        <Text color="red.500" fontSize="sm" mt={1}>
                          {formik.errors.country || formErrors.country}
                        </Text>
                      )}
                    </Box>
                  </HStack>
                  {/* Address Lines */}
                  <Box>
                    <Input 
                      name="officeAddress"
                      value={formik.values.officeAddress} 
                      placeholder="Office Address Line 1 *" 
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      borderColor={
                        (formik.errors.officeAddress && formik.touched.officeAddress) || formErrors.officeAddress 
                          ? "red.300" 
                          : "gray.200"
                      }
                      _hover={{
                        borderColor: (formik.errors.officeAddress && formik.touched.officeAddress) || formErrors.officeAddress 
                          ? "red.400" 
                          : "gray.300"
                      }}
                      _focus={{
                        borderColor: (formik.errors.officeAddress && formik.touched.officeAddress) || formErrors.officeAddress 
                          ? "red.500" 
                          : "blue.500",
                        shadow: (formik.errors.officeAddress && formik.touched.officeAddress) || formErrors.officeAddress 
                          ? "0 0 0 1px var(--chakra-colors-red-500)" 
                          : "0 0 0 1px var(--chakra-colors-blue-500)"
                      }}
                    />
                    {((formik.errors.officeAddress && formik.touched.officeAddress) || formErrors.officeAddress) && (
                      <Text color="red.500" fontSize="sm" mt={1}>
                        {formik.errors.officeAddress || formErrors.officeAddress}
                      </Text>
                    )}
                  </Box>
                  <Box>
                    <Input 
                      name="officeAddressLine2"
                      value={formik.values.officeAddressLine2} 
                      placeholder="Office Address Line 2 (Optional)" 
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      borderColor={
                        (formik.errors.officeAddressLine2 && formik.touched.officeAddressLine2) || formErrors.officeAddressLine2 
                          ? "red.300" 
                          : "gray.200"
                      }
                      _hover={{
                        borderColor: (formik.errors.officeAddressLine2 && formik.touched.officeAddressLine2) || formErrors.officeAddressLine2 
                          ? "red.400" 
                          : "gray.300"
                      }}
                      _focus={{
                        borderColor: (formik.errors.officeAddressLine2 && formik.touched.officeAddressLine2) || formErrors.officeAddressLine2 
                          ? "red.500" 
                          : "blue.500",
                        shadow: (formik.errors.officeAddressLine2 && formik.touched.officeAddressLine2) || formErrors.officeAddressLine2 
                          ? "0 0 0 1px var(--chakra-colors-red-500)" 
                          : "0 0 0 1px var(--chakra-colors-blue-500)"
                      }}
                    />
                    {((formik.errors.officeAddressLine2 && formik.touched.officeAddressLine2) || formErrors.officeAddressLine2) && (
                      <Text color="red.500" fontSize="sm" mt={1}>
                        {formik.errors.officeAddressLine2 || formErrors.officeAddressLine2}
                      </Text>
                    )}
                  </Box>
                </VStack>
              </Box>

              {/* Facilities Section */}
              <Box>
                <FormLabel fontWeight="bold">
                  Facilities <Text as="span" color="red.500">*</Text>
                  <Text fontSize="sm" fontWeight="normal" color="gray.600" mt={1}>
                    Add details about your academy's locations and facilities
                  </Text>
                </FormLabel>
                <VStack spacing={4} align="stretch">
                  {formik.values.facilityAddresses.map((address, idx) => (
                    <Box key={idx} p={6} bg="gray.50" borderWidth={1} borderRadius="lg" borderColor="gray.200">
                      <Flex justify="space-between" align="center" mb={4}>
                        <Text fontSize="lg" fontWeight="semibold" color="gray.700">
                          Facility {idx + 1}
                        </Text>
                        <Button 
                          size="sm" 
                          colorScheme="red" 
                          variant="outline"
                          onClick={() => removeFacilityAddress(idx)} 
                          leftIcon={<FiTrash />}
                        >
                          Remove
                        </Button>
                      </Flex>
                      
                      <VStack spacing={4} align="stretch">
                        {/* Facility Name */}
                        <Box>
                          <FormLabel mb={2} fontSize="sm" fontWeight="medium" color="gray.700">
                            Facility Name <Text as="span" color="red.500">*</Text>
                          </FormLabel>
                          <Input 
                            value={address.name} 
                            placeholder="Enter facility name *" 
                            onChange={e => handleFacilityAddressChange(idx, { ...address, name: e.target.value })}
                            borderColor={
                              facilityErrors[idx]?.name 
                                ? "red.300" 
                                : "gray.200"
                            }
                            _hover={{
                              borderColor: facilityErrors[idx]?.name 
                                ? "red.400" 
                                : "gray.300"
                            }}
                            _focus={{
                              borderColor: facilityErrors[idx]?.name 
                                ? "red.500" 
                                : "blue.500",
                              shadow: facilityErrors[idx]?.name 
                                ? "0 0 0 1px var(--chakra-colors-red-500)" 
                                : "0 0 0 1px var(--chakra-colors-blue-500)"
                            }}
                          />
                          {facilityErrors[idx]?.name && (
                            <Text color="red.500" fontSize="sm" mt={1}>
                              {facilityErrors[idx].name}
                            </Text>
                          )}
                        </Box>

                        {/* Pin Code - First for auto-population */}
                        <Box>
                          <FormLabel mb={2} fontSize="sm" fontWeight="medium" color="gray.700">
                            Pin Code <Text as="span" color="red.500">*</Text>
                          </FormLabel>
                          <Input 
                            value={address.pinCode} 
                            placeholder="Enter Pin Code to auto-populate location details *" 
                            onChange={e => {
                              const pincode = e.target.value;
                              // Only allow numeric input and limit to reasonable length
                              if (/^\d*$/.test(pincode) && pincode.length <= 8) {
                                handleFacilityAddressChange(idx, { ...address, pinCode: pincode });
                                
                                // Trigger validation when user types exactly 6 digits
                                if (pincode.length === 6) {
                                  getDetailsFromPincodeForFacility(pincode, idx);
                                } else if (pincode.length !== 6) {
                                  setFacilityPincodeErrors(prev => ({
                                    ...prev,
                                    [idx]: false
                                  }));
                                }
                              }
                            }}
                            borderColor={
                              facilityPincodeErrors[idx] || facilityErrors[idx]?.pinCode 
                                ? "red.300" 
                                : "gray.200"
                            }
                            _hover={{
                              borderColor: facilityPincodeErrors[idx] || facilityErrors[idx]?.pinCode 
                                ? "red.400" 
                                : "gray.300"
                            }}
                            _focus={{
                              borderColor: facilityPincodeErrors[idx] || facilityErrors[idx]?.pinCode 
                                ? "red.500" 
                                : "blue.500",
                              shadow: facilityPincodeErrors[idx] || facilityErrors[idx]?.pinCode 
                                ? "0 0 0 1px var(--chakra-colors-red-500)" 
                                : "0 0 0 1px var(--chakra-colors-blue-500)"
                            }}
                          />
                          {(facilityPincodeErrors[idx] || facilityErrors[idx]?.pinCode) && (
                            <Text color="red.500" fontSize="sm" mt={1}>
                              {facilityErrors[idx]?.pinCode || "Please enter a valid 6-digit pincode"}
                            </Text>
                          )}
                        </Box>

                        {/* City, State, Country Row */}
                        <HStack spacing={4} align="flex-start">
                          <Box flex={1}>
                            <FormLabel mb={2} fontSize="sm" fontWeight="medium" color="gray.700">
                              City <Text as="span" color="red.500">*</Text>
                            </FormLabel>
                            <Input 
                              value={address.city} 
                              placeholder="City *" 
                              onChange={e => handleFacilityAddressChange(idx, { ...address, city: e.target.value })}
                              borderColor={
                                facilityErrors[idx]?.city 
                                  ? "red.300" 
                                  : "gray.200"
                              }
                              _hover={{
                                borderColor: facilityErrors[idx]?.city 
                                  ? "red.400" 
                                  : "gray.300"
                              }}
                              _focus={{
                                borderColor: facilityErrors[idx]?.city 
                                  ? "red.500" 
                                  : "blue.500",
                                shadow: facilityErrors[idx]?.city 
                                  ? "0 0 0 1px var(--chakra-colors-red-500)" 
                                  : "0 0 0 1px var(--chakra-colors-blue-500)"
                              }}
                            />
                            {facilityErrors[idx]?.city && (
                              <Text color="red.500" fontSize="sm" mt={1}>
                                {facilityErrors[idx].city}
                              </Text>
                            )}
                          </Box>
                          <Box flex={1}>
                            <FormLabel mb={2} fontSize="sm" fontWeight="medium" color="gray.700">
                              State <Text as="span" color="red.500">*</Text>
                            </FormLabel>
                            <Select 
                              value={address.state} 
                              placeholder="Select State *" 
                              onChange={e => handleFacilityAddressChange(idx, { ...address, state: e.target.value })}
                              borderColor={
                                facilityErrors[idx]?.state 
                                  ? "red.300" 
                                  : "gray.200"
                              }
                              _hover={{
                                borderColor: facilityErrors[idx]?.state 
                                  ? "red.400" 
                                  : "gray.300"
                              }}
                              _focus={{
                                borderColor: facilityErrors[idx]?.state 
                                  ? "red.500" 
                                  : "blue.500",
                                shadow: facilityErrors[idx]?.state 
                                  ? "0 0 0 1px var(--chakra-colors-red-500)" 
                                  : "0 0 0 1px var(--chakra-colors-blue-500)"
                              }}
                            >
                              {states.map(state => (
                                <option key={state.isoCode} value={state.name}>
                                  {state.name}
                                </option>
                              ))}
                            </Select>
                            {facilityErrors[idx]?.state && (
                              <Text color="red.500" fontSize="sm" mt={1}>
                                {facilityErrors[idx].state}
                              </Text>
                            )}
                          </Box>
                          <Box flex={1}>
                            <FormLabel mb={2} fontSize="sm" fontWeight="medium" color="gray.700">
                              Country <Text as="span" color="red.500">*</Text>
                            </FormLabel>
                            <Input 
                              value={address.country} 
                              placeholder="Country *" 
                              onChange={e => handleFacilityAddressChange(idx, { ...address, country: e.target.value })}
                              borderColor={
                                facilityErrors[idx]?.country 
                                  ? "red.300" 
                                  : "gray.200"
                              }
                              _hover={{
                                borderColor: facilityErrors[idx]?.country 
                                  ? "red.400" 
                                  : "gray.300"
                              }}
                              _focus={{
                                borderColor: facilityErrors[idx]?.country 
                                  ? "red.500" 
                                  : "blue.500",
                                shadow: facilityErrors[idx]?.country 
                                  ? "0 0 0 1px var(--chakra-colors-red-500)" 
                                  : "0 0 0 1px var(--chakra-colors-blue-500)"
                              }}
                            />
                            {facilityErrors[idx]?.country && (
                              <Text color="red.500" fontSize="sm" mt={1}>
                                {facilityErrors[idx].country}
                              </Text>
                            )}
                          </Box>
                        </HStack>

                        {/* Address Section */}
                        <Box>
                          <FormLabel mb={2} fontSize="sm" fontWeight="medium" color="gray.700">
                            Address <Text as="span" color="red.500">*</Text>
                          </FormLabel>
                          <VStack spacing={3} align="stretch">
                            <Input 
                              value={address.addressLine1} 
                              placeholder="Address Line 1 *" 
                              onChange={e => handleFacilityAddressChange(idx, { ...address, addressLine1: e.target.value })}
                              borderColor={
                                facilityErrors[idx]?.addressLine1 
                                  ? "red.300" 
                                  : "gray.200"
                              }
                              _hover={{
                                borderColor: facilityErrors[idx]?.addressLine1 
                                  ? "red.400" 
                                  : "gray.300"
                              }}
                              _focus={{
                                borderColor: facilityErrors[idx]?.addressLine1 
                                  ? "red.500" 
                                  : "blue.500",
                                shadow: facilityErrors[idx]?.addressLine1 
                                  ? "0 0 0 1px var(--chakra-colors-red-500)" 
                                  : "0 0 0 1px var(--chakra-colors-blue-500)"
                              }}
                            />
                            {facilityErrors[idx]?.addressLine1 && (
                              <Text color="red.500" fontSize="sm" mt={1}>
                                {facilityErrors[idx].addressLine1}
                              </Text>
                            )}
                            <Input 
                              value={address.addressLine2} 
                              placeholder="Address Line 2 (Optional)" 
                              onChange={e => handleFacilityAddressChange(idx, { ...address, addressLine2: e.target.value })}
                              borderColor={
                                facilityErrors[idx]?.addressLine2 
                                  ? "red.300" 
                                  : "gray.200"
                              }
                              _hover={{
                                borderColor: facilityErrors[idx]?.addressLine2 
                                  ? "red.400" 
                                  : "gray.300"
                              }}
                              _focus={{
                                borderColor: facilityErrors[idx]?.addressLine2 
                                  ? "red.500" 
                                  : "blue.500",
                                shadow: facilityErrors[idx]?.addressLine2 
                                  ? "0 0 0 1px var(--chakra-colors-red-500)" 
                                  : "0 0 0 1px var(--chakra-colors-blue-500)"
                              }}
                            />
                            {facilityErrors[idx]?.addressLine2 && (
                              <Text color="red.500" fontSize="sm" mt={1}>
                                {facilityErrors[idx].addressLine2}
                              </Text>
                            )}
                          </VStack>
                        </Box>

                        {/* Amenities */}
                        <Box>
                          <FormLabel mb={2} fontSize="sm" fontWeight="medium" color="gray.700">
                            Amenities
                          </FormLabel>
                          <Box border="1px solid" borderColor={facilityErrors[idx]?.amenities ? "red.300" : "gray.200"} borderRadius="md" p={2}>
                            <ReactQuill
                              theme="snow"
                              value={address.amenities}
                              onChange={value => handleFacilityAddressChange(idx, { ...address, amenities: value })}
                              style={{ minHeight: "80px" }}
                            />
                          </Box>
                          {facilityErrors[idx]?.amenities && (
                            <Text color="red.500" fontSize="sm" mt={1}>
                              {facilityErrors[idx].amenities}
                            </Text>
                          )}
                        </Box>
                      </VStack>
                    </Box>
                  ))}
                  
                  {/* Add Another Facility Button */}
                  <Box 
                    border="2px dashed" 
                    borderColor="gray.300" 
                    borderRadius="lg" 
                    textAlign="center"
                    _hover={{ borderColor: "blue.400", bg: "blue.50" }}
                    transition="all 0.2s"
                    cursor="pointer"
                    onClick={addFacilityAddress}
                  >
                    <Button 
                      size="lg" 
                      colorScheme="blue" 
                      variant="ghost"
                      leftIcon={<FiPlus />}
                      onClick={addFacilityAddress}
                    >
                      Add Another Facility
                    </Button>
                  </Box>
                </VStack>
              </Box>

              {/* Documents Section */}
              <Box>
                <FormLabel fontWeight="bold" mb={4}>
                  Required Documents <Text as="span" color="red.500">*</Text>
                  <Text fontSize="sm" fontWeight="normal" color="gray.600" mt={1}>
                    Provide essential documents for academy verification
                  </Text>
                </FormLabel>
                <VStack align="stretch" spacing={4}>
                  <HStack spacing={4} align="flex-start">
                    <Box flex={1}>
                      <FormLabel mb={2} fontSize="sm" fontWeight="medium" color="gray.700">
                        PAN Number <Text as="span" color="red.500">*</Text>
                      </FormLabel>
                      <Input 
                        name="panNumber"
                        value={formik.values.panNumber} 
                        placeholder="Enter PAN Number" 
                        onChange={formik.handleChange} 
                        onBlur={formik.handleBlur}
                        borderColor={
                          (formik.errors.panNumber && formik.touched.panNumber) || formErrors.panNumber 
                            ? "red.300" 
                            : "gray.200"
                        }
                        _hover={{
                          borderColor: (formik.errors.panNumber && formik.touched.panNumber) || formErrors.panNumber 
                            ? "red.400" 
                            : "gray.300"
                        }}
                        _focus={{
                          borderColor: (formik.errors.panNumber && formik.touched.panNumber) || formErrors.panNumber 
                            ? "red.500" 
                            : "blue.500",
                          shadow: (formik.errors.panNumber && formik.touched.panNumber) || formErrors.panNumber 
                            ? "0 0 0 1px var(--chakra-colors-red-500)" 
                            : "0 0 0 1px var(--chakra-colors-blue-500)"
                        }}
                      />
                      {((formik.errors.panNumber && formik.touched.panNumber) || formErrors.panCardFile) && (
                        <Text color="red.500" fontSize="sm" mt={1}>
                          {formik.errors.panNumber || formErrors.panCardFile}
                        </Text>
                      )}
                    </Box>
                    <Box flex={1}>
                      <FormLabel mb={2} fontSize="sm" fontWeight="medium" color="gray.700">
                        PAN Image <Text as="span" color="red.500">*</Text>
                      </FormLabel>
                      <Box
                        border="2px dashed"
                        borderColor={formik.values.panCard ? "green.300" : "gray.300"}
                        borderRadius="md"
                        p={formik.values.panCard ? 2 : 8}
                        textAlign="center"
                        cursor="pointer"
                        transition="all 0.2s"
                        _hover={{ borderColor: "blue.400", bg: "blue.50" }}
                        onClick={() => document.getElementById('pan-upload').click()}
                        bg={formik.values.panCard ? "green.50" : "gray.50"}
                        position="relative"
                      >
                        {formik.values.panCard ? (
                          <VStack spacing={2}>
                            <Box position="relative" w="full">
                              <Image
                                src={URL.createObjectURL(formik.values.panCard)}
                                alt="PAN Card"
                                maxH="150px"
                                objectFit="contain"
                                borderRadius="md"
                                mx="auto"
                              />
                              <Box
                                position="absolute"
                                top={1}
                                right={1}
                                bg="red.500"
                                color="white"
                                borderRadius="full"
                                p={1}
                                cursor="pointer"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  formik.setFieldValue('panCard', null);
                                  formik.setFieldValue('panCardFile', null);
                                  // Reset the file input
                                  const fileInput = document.getElementById('pan-upload');
                                  if (fileInput) fileInput.value = '';
                                }}
                                _hover={{ bg: "red.600" }}
                              >
                                <Icon as={FiTrash} boxSize={3} />
                              </Box>
                            </Box>
                            <Text color="green.600" fontSize="sm" fontWeight="medium">
                              Click to change image
                            </Text>
                          </VStack>
                        ) : (
                          <VStack spacing={2}>
                            <Icon as={FiPlus} boxSize={6} color="gray.400" />
                            <Text color="gray.500" fontSize="sm">
                              Click to upload
                            </Text>
                            <Text color="gray.500" fontSize="sm">
                              PAN Card
                            </Text>
                          </VStack>
                        )}
                      </Box>
                      <Input 
                        id="pan-upload"
                        type="file" 
                        display="none"
                        accept="image/*,application/pdf" 
                        onChange={e => {
                          const file = e.target.files[0];
                          if (file) {
                            formik.setFieldValue('panCard', file);
                            formik.setFieldValue('panCardFile', file);
                          }
                        }}
                      />
                      {((formik.errors.panCardFile && formik.touched.panCardFile) || formErrors.panCardFile) && (
                        <Text color="red.500" fontSize="sm" mt={1}>
                          {formik.errors.panCardFile || formErrors.panCardFile}
                        </Text>
                      )}
                      {formik.values.panCard && (
                        <Text color="green.500" fontSize="sm" mt={1}>
                          ✓ File uploaded successfully
                        </Text>
                      )}
                    </Box>
                  </HStack>

                  {/* Aadhaar Card Section */}
                  <HStack spacing={4} align="flex-start">
                    <Box flex={1}>
                      <FormLabel mb={2} fontSize="sm" fontWeight="medium" color="gray.700">
                        Aadhaar Number <Text as="span" color="red.500">*</Text>
                      </FormLabel>
                      <Input 
                        name="aadhaarNo"
                        value={formik.values.aadhaarNo} 
                        placeholder="Enter Aadhaar No." 
                        onChange={formik.handleChange} 
                        onBlur={formik.handleBlur}
                        borderColor={
                          (formik.errors.aadhaarNo && formik.touched.aadhaarNo) || formErrors.aadhaarNo 
                            ? "red.300" 
                            : "gray.200"
                        }
                        _hover={{
                          borderColor: (formik.errors.aadhaarNo && formik.touched.aadhaarNo) || formErrors.aadhaarNo 
                            ? "red.400" 
                            : "gray.300"
                        }}
                        _focus={{
                          borderColor: (formik.errors.aadhaarNo && formik.touched.aadhaarNo) || formErrors.aadhaarNo 
                            ? "red.500" 
                            : "blue.500",
                          outline: "none",
                          shadow: (formik.errors.aadhaarNo && formik.touched.aadhaarNo) || formErrors.aadhaarNo 
                            ? "0 0 0 1px var(--chakra-colors-red-500)" 
                            : "0 0 0 1px var(--chakra-colors-blue-500)"
                        }}
                      />
                      {((formik.errors.aadhaarNo && formik.touched.aadhaarNo) || formErrors.aadhaarNo) && (
                        <Text color="red.500" fontSize="sm" mt={1}>
                          {formik.errors.aadhaarNo || formErrors.aadhaarNo}
                        </Text>
                      )}
                    </Box>

                    <Box flex={1}>
                      <FormLabel mb={2} fontSize="sm" fontWeight="medium" color="gray.700">
                        Aadhaar Image <Text as="span" color="red.500">*</Text>
                      </FormLabel>
                      <Box
                        border="2px dashed"
                        borderColor={formik.values.aadhaarCard ? "green.300" : "gray.300"}
                        borderRadius="md"
                        p={formik.values.aadhaarCard ? 2 : 8}
                        textAlign="center"
                        cursor="pointer"
                        transition="all 0.2s"
                        _hover={{ borderColor: "blue.400", bg: "blue.50" }}
                        onClick={() => document.getElementById('aadhaar-upload').click()}
                        bg={formik.values.aadhaarCard ? "green.50" : "gray.50"}
                        position="relative"
                      >
                        {formik.values.aadhaarCard ? (
                          <VStack spacing={2}>
                            <Box position="relative" w="full">
                              <Image
                                src={URL.createObjectURL(formik.values.aadhaarCard)}
                                alt="Aadhaar Card"
                                maxH="150px"
                                objectFit="contain"
                                borderRadius="md"
                                mx="auto"
                              />
                              <Box
                                position="absolute"
                                top={1}
                                right={1}
                                bg="red.500"
                                color="white"
                                borderRadius="full"
                                p={1}
                                cursor="pointer"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  formik.setFieldValue('aadhaarCard', null);
                                  formik.setFieldValue('aadhaarCardFile', null);
                                  // Reset the file input
                                  const fileInput = document.getElementById('aadhaar-upload');
                                  if (fileInput) fileInput.value = '';
                                }}
                                _hover={{ bg: "red.600" }}
                              >
                                <Icon as={FiTrash} boxSize={3} />
                              </Box>
                            </Box>
                            <Text color="green.600" fontSize="sm" fontWeight="medium">
                              Click to change image
                            </Text>
                          </VStack>
                        ) : (
                          <VStack spacing={2}>
                            <Icon as={FiPlus} boxSize={6} color="gray.400" />
                            <Text color="gray.500" fontSize="sm">
                              Click to upload
                            </Text>
                            <Text color="gray.500" fontSize="sm">
                              Aadhaar Card
                            </Text>
                          </VStack>
                        )}
                      </Box>
                      <Input 
                        id="aadhaar-upload"
                        type="file" 
                        display="none"
                        name="aadhaarCardFile"
                        onChange={(e) => {
                          const file = e.target.files[0];
                          if (file) {
                            formik.setFieldValue('aadhaarCard', file);
                            formik.setFieldValue('aadhaarCardFile', file.name);
                          }
                        }}
                        onBlur={formik.handleBlur}
                        accept=".pdf,.jpg,.jpeg,.png"
                      />
                      {formik.values.aadhaarCard && (
                        <Text color="green.500" fontSize="sm" mt={1}>
                          ✓ File uploaded successfully
                        </Text>
                      )}
                    </Box>
                  </HStack>

                  {/* Account Details Section */}
                  <HStack spacing={4} align="flex-start">
                    <Box flex={1}>
                      <FormLabel mb={2} fontSize="sm" fontWeight="medium" color="gray.700">
                        Account No. <Text as="span" color="red.500">*</Text>
                      </FormLabel>
                      <Input 
                        name="accountNo"
                        value={formik.values.accountNo} 
                        placeholder="Enter account no. *" 
                        onChange={formik.handleChange} 
                        onBlur={formik.handleBlur}
                        borderColor={
                          (formik.errors.accountNo && formik.touched.accountNo) || formErrors.accountNo 
                            ? "red.300" 
                            : "gray.200"
                        }
                        _hover={{
                          borderColor: (formik.errors.accountNo && formik.touched.accountNo) || formErrors.accountNo 
                            ? "red.400" 
                            : "gray.300"
                        }}
                        _focus={{
                          borderColor: (formik.errors.accountNo && formik.touched.accountNo) || formErrors.accountNo 
                            ? "red.500" 
                            : "blue.500",
                          shadow: (formik.errors.accountNo && formik.touched.accountNo) || formErrors.accountNo 
                            ? "0 0 0 1px var(--chakra-colors-red-500)" 
                            : "0 0 0 1px var(--chakra-colors-blue-500)"
                        }}
                      />
                      {((formik.errors.accountNo && formik.touched.accountNo) || formErrors.accountNo) && (
                        <Text color="red.500" fontSize="sm" mt={1}>
                          {formik.errors.accountNo || formErrors.accountNo}
                        </Text>
                      )}
                    </Box>
                    <Box flex={1}>
                      <FormLabel mb={2} fontSize="sm" fontWeight="medium" color="gray.700">
                        IFSC Code <Text as="span" color="red.500">*</Text>
                      </FormLabel>
                      <Input 
                        name="ifscCode"
                        value={formik.values.ifscCode} 
                        placeholder="Enter IFSC code *" 
                        onChange={formik.handleChange} 
                        onBlur={formik.handleBlur}
                        borderColor={
                          (formik.errors.ifscCode && formik.touched.ifscCode) || formErrors.ifscCode 
                            ? "red.300" 
                            : "gray.200"
                        }
                        _hover={{
                          borderColor: (formik.errors.ifscCode && formik.touched.ifscCode) || formErrors.ifscCode 
                            ? "red.400" 
                            : "gray.300"
                        }}
                        _focus={{
                          borderColor: (formik.errors.ifscCode && formik.touched.ifscCode) || formErrors.ifscCode 
                            ? "red.500" 
                            : "blue.500",
                          shadow: (formik.errors.ifscCode && formik.touched.ifscCode) || formErrors.ifscCode 
                            ? "0 0 0 1px var(--chakra-colors-red-500)" 
                            : "0 0 0 1px var(--chakra-colors-blue-500)"
                        }}
                      />
                      {((formik.errors.ifscCode && formik.touched.ifscCode) || formErrors.ifscCode) && (
                        <Text color="red.500" fontSize="sm" mt={1}>
                          {formik.errors.ifscCode || formErrors.ifscCode}
                        </Text>
                      )}
                    </Box>
                  </HStack>

                  {/* GST Number */}
                  <Box>
                    <FormLabel mb={2} fontSize="sm" fontWeight="medium" color="gray.700">
                      GST Number <Text as="span" color="red.500">*</Text>
                    </FormLabel>
                    <Input 
                      name="gstNumber"
                      value={formik.values.gstNumber} 
                      placeholder="Enter GST Number *" 
                      onChange={formik.handleChange} 
                      onBlur={formik.handleBlur}
                      borderColor={
                        (formik.errors.gstNumber && formik.touched.gstNumber) || formErrors.gstNumber 
                          ? "red.300" 
                          : "gray.200"
                      }
                      _hover={{
                        borderColor: (formik.errors.gstNumber && formik.touched.gstNumber) || formErrors.gstNumber 
                          ? "red.400" 
                          : "gray.300"
                      }}
                      _focus={{
                        borderColor: (formik.errors.gstNumber && formik.touched.gstNumber) || formErrors.gstNumber 
                          ? "red.500" 
                          : "blue.500",
                        shadow: (formik.errors.gstNumber && formik.touched.gstNumber) || formErrors.gstNumber 
                          ? "0 0 0 1px var(--chakra-colors-red-500)" 
                          : "0 0 0 1px var(--chakra-colors-blue-500)"
                      }}
                    />
                    {((formik.errors.gstNumber && formik.touched.gstNumber) || formErrors.gstNumber) && (
                      <Text color="red.500" fontSize="sm" mt={1}>
                        {formik.errors.gstNumber || formErrors.gstNumber}
                      </Text>
                    )}
                  </Box>

                  {/* Platform Fee */}
                  <FormControl isInvalid={formik.errors.platformFee && formik.touched.platformFee}>
                    <FormLabel fontWeight="bold">
                      Platform Fee <Text as="span" color="red.500">*</Text>
                    </FormLabel>
                    <Input 
                      name="platformFee"
                      value={formik.values.platformFee} 
                      placeholder="Enter platform fee *" 
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      borderColor={
                        (formik.errors.platformFee && formik.touched.platformFee) || formErrors.platformFee 
                          ? "red.300" 
                          : "gray.200"
                      }
                      _hover={{
                        borderColor: (formik.errors.platformFee && formik.touched.platformFee) || formErrors.platformFee 
                          ? "red.400" 
                          : "gray.300"
                      }}
                      _focus={{
                        borderColor: (formik.errors.platformFee && formik.touched.platformFee) || formErrors.platformFee 
                          ? "red.500" 
                          : "blue.500",
                        shadow: (formik.errors.platformFee && formik.touched.platformFee) || formErrors.platformFee 
                          ? "0 0 0 1px var(--chakra-colors-red-500)" 
                          : "0 0 0 1px var(--chakra-colors-blue-500)"
                      }}
                    />
                    <FormErrorMessage>{formik.errors.platformFee}</FormErrorMessage>
                    {formErrors.platformFee && !formik.errors.platformFee && (
                      <Text color="red.500" fontSize="sm" mt={1}>
                        {formErrors.platformFee}
                      </Text>
                    )}
                  </FormControl>
                </VStack>
              </Box>

              {/* Sports Categories Section */}
              <Box ref={categoryInputRef} position="relative">
                <FormLabel fontWeight="bold" mb={3}>
                  Sports Categories <Text as="span" color="red.500">*</Text>
                  <Text fontSize="sm" fontWeight="normal" color="gray.600" mt={1}>
                    Select the sports categories your academy offers
                  </Text>
                </FormLabel>
                <InputGroup>
                  <Input
                    readOnly
                    value={selectedCategories
                      .map(cid => categories.find(cat => cat._id === cid)?.name)
                      .filter(Boolean)
                      .join(", ")}
                    placeholder="Click to select sports categories *"
                    onClick={() => setCategoryDropdownOpen(!categoryDropdownOpen)}
                    cursor="pointer"
                    bg="white"
                    borderColor={
                      (formik.errors.selectedCategories && formik.touched.selectedCategories) || formErrors.selectedCategories 
                        ? "red.300" 
                        : "gray.200"
                    }
                    _hover={{
                      borderColor: (formik.errors.selectedCategories && formik.touched.selectedCategories) || formErrors.selectedCategories 
                        ? "red.400" 
                        : "gray.300"
                    }}
                    _focus={{
                      borderColor: (formik.errors.selectedCategories && formik.touched.selectedCategories) || formErrors.selectedCategories 
                        ? "red.500" 
                        : "blue.500",
                      shadow: (formik.errors.selectedCategories && formik.touched.selectedCategories) || formErrors.selectedCategories 
                        ? "0 0 0 1px var(--chakra-colors-red-500)" 
                        : "0 0 0 1px var(--chakra-colors-blue-500)"
                    }}
                  />
                  <InputRightElement pointerEvents="none">
                    <FiChevronDown color={categoryDropdownOpen ? "blue" : "gray"} />
                  </InputRightElement>
                </InputGroup>
                {((formik.errors.selectedCategories && formik.touched.selectedCategories) || formErrors.selectedCategories) && (
                  <Text color="red.500" fontSize="sm" mt={1}>
                    {formik.errors.selectedCategories || formErrors.selectedCategories}
                  </Text>
                )}
                <Flex mt={3} gap={2} wrap="wrap">
                  {selectedCategories.map(cid => {
                    const cat = categories.find(cat => cat._id === cid);
                    return cat ? (
                      <Tag key={cid} borderRadius="full" variant="solid" colorScheme="teal" size="md">
                        <Image src={cat.image} alt={cat.name} boxSize="20px" borderRadius="full" mr={2} />
                        <TagLabel>{cat.name}</TagLabel>
                        <TagCloseButton onClick={() => removeCategory(cid)} />
                      </Tag>
                    ) : null;
                  })}
                </Flex>
                {categoryDropdownOpen && (
                  <Box position="absolute" zIndex={10} bg="white" borderWidth={1} borderRadius="md" boxShadow="lg" mb={2} w="100%" maxH="250px" overflowY="auto" p={4} bottom="100%">
                    <CheckboxGroup value={selectedCategories}>
                      <Stack>
                        {categories.map(cat => (
                          <Checkbox
                            key={cat._id}
                            value={cat._id}
                            isChecked={selectedCategories.includes(cat._id)}
                            onChange={() => handleCategorySelect(cat._id)}
                          >
                            <Flex align="center" gap={2}>
                              <Image src={cat.image} alt={cat.name} boxSize="24px" borderRadius="full" />
                              {cat.name}
                            </Flex>
                          </Checkbox>
                        ))}
                      </Stack>
                    </CheckboxGroup>
                  </Box>
                )}
              </Box>
            </Stack>
         <Button 
          colorScheme="teal" 
          mt={8} 
          w="full" 
          size="lg" 
          type="submit"
          fontWeight="bold"
          isLoading={formik.isSubmitting}
          loadingText="Registering..."
        >
          Register
        </Button>
        </Box>
      </Layout>
      </form>
    </Box>
  );
};

export default AcademyCreation;
