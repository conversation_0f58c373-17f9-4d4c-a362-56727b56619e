import React, { useEffect, useState } from "react";
import Layout from "../../layout/default";
import { Md<PERSON><PERSON><PERSON>, MdD<PERSON><PERSON><PERSON>le } from "react-icons/md";
import {
  Box,
  BreadcrumbLink,
  Input,
  Breadcrumb,
  BreadcrumbItem,
  Card,
  CardBody,
  Button,
  Stack,
  Text,
  Flex,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  TableContainer,
  Table,
  Tbody,
  Tr,
  Td,
  Thead,
  Th,
  ListItem,
  UnorderedList,
  useToast,
  Spinner,
  Heading,
  AlertDialog,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogCloseButton,
  AlertDialogBody,
  AlertDialogFooter,
  Badge,
  Tooltip,
} from "@chakra-ui/react";
import { Link } from "react-router-dom";
import axios from "axios";
import { getCookie } from "../../utilities/auth";
import { useSelector } from "react-redux";

const TopAcademies = () => {
  const [academyData, setAcademyData] = useState({
    result: [],
    isLoading: false,
    error: false,
  });
  const [cmsAcademyData, setCmsAcademyData] = useState({
    result: [],
    isLoading: false,
    error: false,
  });

  const [isUpdated, setIsUpdated] = useState(false);
  const [draggedItemIndex, setDraggedItemIndex] = useState(null);
  const [saveChangesBtnLoading, setSaveChangesBtnLoading] = useState(false);
  const [adjustBtnEdit, setAdjustBtnEdit] = useState(false);
  const [prevAcademyData, setPrevAcademyData] = useState([]);
  const [posBtnLoading, setPosBtnLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [isDeleted, setIsDeleted] = useState(false);
  const { isOpen, onOpen, onClose } = useDisclosure();

  const [isOpen1, setIsOpen1] = useState(false);
  const onClose1 = () => setIsOpen1(false);
  const onOpen1 = () => setIsOpen1(true);

  const toast = useToast();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const userData = useSelector((state) => state.user);

  const getAcademyData = (query) => {
    setAcademyData({ result: [], isLoading: true, error: false });
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/academy?${
        searchQuery.length > 0
          ? `page=1&name=${query}&authStatus=authorized&status=active`
          : "page=1&authStatus=authorized&status=active"
      }`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        setAcademyData({
          result: Array.isArray(response.data.data?.academies) ? response.data.data?.academies : [],
          isLoading: false,
          error: false,
        });
      })
      .catch((error) => {
        console.log(error);
        setAcademyData({ result: [], isLoading: false, error: true });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const getCmsAcademyData = () => {
    setCmsAcademyData({ result: [], isLoading: true, error: false });
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/cms/academy`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };
    axios
      .request(config)
      .then((response) => {
        setCmsAcademyData({
          result: Array.isArray(response.data) ? response.data.sort((a, b) => a.position - b.position) : [],
          isLoading: false,
          error: false,
        });
      })
      .catch((error) => {
        console.log(error);
        setCmsAcademyData({ result: [], isLoading: false, error: true });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const updateAndSave = () => {
    onClose1();
    setSaveChangesBtnLoading(true);
    const getIds = cmsAcademyData?.result.map((id, index) => {
      return { academy: id?.academy?._id, position: index + 1 };
    });

    let data = JSON.stringify({
      documents: getIds,
    });
    let config = {
      method: "post",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/cms/academy/add`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
     ;
        getCmsAcademyData();
        getAcademyData(searchQuery);
        setSaveChangesBtnLoading(false);
        setIsUpdated(false);
        onClose();
        setIsDeleted(false);
        toast({
          title: "Academy updated successfully",
          status: "success",
          duration: 3500,
          position: "top",
          isClosable: true,
        });
      })
      .catch((error) => {
        console.log(error);
        setSaveChangesBtnLoading(false);
        if (
          error.response.data.message === "Top Academy cannot be greater than 15"
        ) {
          getCmsAcademyData();
          setIsUpdated(false);
          toast({
            title: error.response.data.message,
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const handleDragStart = (index) => {
    setDraggedItemIndex(index);
  };

  const handleDragOver = (index) => {
    if (draggedItemIndex === null) return;
    const newItems = [...cmsAcademyData.result]; // Copy the result array from blockData
    const draggedItem = newItems[draggedItemIndex];
    newItems.splice(draggedItemIndex, 1);
    newItems.splice(index, 0, draggedItem);

    // Adjust positions of items inside newItems array
    newItems.forEach((item, idx) => {
      item.position = idx + 1;
    });

    setCmsAcademyData({ ...cmsAcademyData, result: newItems }); // Update result array in blockData
    setDraggedItemIndex(index);
  };

  const handleDragEnd = () => {
    setDraggedItemIndex(null);
  };

  const updateBlockPosition = () => {
    setPosBtnLoading(true);
    const value = cmsAcademyData?.result.map((x) => {
      return { id: x._id, newPosition: x.position };
    });
    let data = JSON.stringify({
      updates: value,
    });

    let config = {
      method: "patch",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/cms/academy/position`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
        setPosBtnLoading(false);
        setPrevAcademyData([]);
        setAdjustBtnEdit(false);
        toast({
          title: "Academies position updated",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      })
      .catch((error) => {
        console.log(error);
        onClose1();
        setPosBtnLoading(false);
        setPrevAcademyData([]);
        setAdjustBtnEdit(false);
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  useEffect(() => {
    getCmsAcademyData();
  }, []);

  return (
    <Layout title="CMS | Top Academy" content="container">
      <Flex justifyContent={"space-between"} alignItems={"center"}>
        <Breadcrumb fontWeight="medium" fontSize="sm">
          <BreadcrumbItem>
            <BreadcrumbLink>
              <Link to={"/"}>Dashboard</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>

          <BreadcrumbItem isCurrentPage>
            <BreadcrumbLink>CMS</BreadcrumbLink>
          </BreadcrumbItem>

          <BreadcrumbItem isCurrentPage>
            <BreadcrumbLink href="#">Top Academies</BreadcrumbLink>
          </BreadcrumbItem>
        </Breadcrumb>
        {userData?.accessScopes?.cms?.includes("write") && (
          <>
          {isDeleted ? (
              !(!cmsAcademyData?.isLoading && cmsAcademyData?.error) &&
              !adjustBtnEdit &&
              userData?.accessScopes?.cms?.includes("write") && (
                <Flex justifyContent={"flex-end"} alignItems={"center"}>
                  <Button
                    variant={"outline"}
                    colorScheme="red"
                    size={"sm"}
                    py={5}
                    px={4}
                    mr={4}
                    isDisabled={!isUpdated}
                    onClick={() => {
                      setIsDeleted(false);
                      getCmsAcademyData();
                      setIsUpdated(false);
                    }}
                  >
                    Discard
                  </Button>
                  <Button
                    variant={"outline"}
                    colorScheme="green"
                    size={"sm"}
                    py={5}
                    px={4}
                    isDisabled={!isUpdated}
                    onClick={onOpen1}
                    isLoading={saveChangesBtnLoading}
                  >
                    Save Changes
                  </Button>
                </Flex>
              )
            ) : (
            <Flex>
              {!adjustBtnEdit ? (
                <Box>
                  <Button
                    variant={"outline"}
                    colorScheme="telegram"
                    size={"sm"}
                    py={5}
                    px={4}
                    mr={3}
                    onClick={() => {
                      setAdjustBtnEdit(true);
                      setPrevAcademyData(cmsAcademyData.result);
                    }}
                  >
                    Adjust Position
                  </Button>
                  <Button
                    variant={"outline"}
                    colorScheme="teal"
                    size={"sm"}
                    py={5}
                    px={4}
                    onClick={() => {
                      onOpen();
                      getAcademyData("");
                    }}
                    mr={2}
                    isDisabled={!cmsAcademyData?.isLoading && cmsAcademyData?.error}
                  >
                    Add Academy
                  </Button>
                </Box>
              ) : (
                <Flex>
                  <Button
                    variant={"outline"}
                    colorScheme="red"
                    size={"sm"}
                    py={5}
                    px={4}
                    mr={4}
                    onClick={() => {
                      setCmsAcademyData({
                        result: prevAcademyData,
                        isLoading: false,
                        error: false,
                      });
                      setAdjustBtnEdit(false);
                    }}
                  >
                    Discard
                  </Button>
                  <Button
                    variant={"outline"}
                    colorScheme="green"
                    size={"sm"}
                    py={5}
                    px={4}
                    isLoading={posBtnLoading}
                    onClick={updateBlockPosition}
                  >
                    Save Changes
                  </Button>
                </Flex>
              )}
            </Flex>
        )}
          </>
        )}
      </Flex>
      {/* Added/Selected Course List */}
      {!cmsAcademyData?.isLoading && cmsAcademyData?.error ? (
        <Flex
          justifyContent={"center"}
          alignItems={"center"}
          w={"full"}
          my={10}
        >
          <Text color={"red.500"}>
            Something went wrong please try again later...
          </Text>
        </Flex>
      ) : (
        <TableContainer
          mt={6}
          height={`${window.innerHeight - 200}px`}
          overflowY={"scroll"}
        >
          <Table variant="simple">
            <Thead
              bgColor={"#c1eaee"}
              position={"sticky"}
              top={"0px"}
              zIndex={"99"}
            >
              <Tr bgColor={"#E2DFDF"}>
                <Th>Position</Th>
                <Th>Academy Name</Th>
                <Th>Email</Th>
                <Th>Categories</Th>
                <Th>Status</Th>
                <Th>Auth Status</Th>
                <Th>GST Number</Th>
                <Th textAlign={"center"}>Action</Th>
              </Tr>
            </Thead>
            <Tbody>
              {(cmsAcademyData?.isLoading && !cmsAcademyData?.error) ? (
                <Tr>
                  <Td></Td>
                  <Td> </Td>
                  <Td></Td>
                  <Td
                    display={"flex"}
                    justifyContent={"center"}
                    alignItems={"center"}
                  >
                    <Spinner />
                  </Td>
                  <Td></Td>
                  <Td></Td>
                  <Td></Td>
                </Tr>
              ) : (
                (cmsAcademyData?.result || []).map((academyData, inx) => {
                  return (
                    <>
                      {adjustBtnEdit ? (
                        <Tr
                          key={academyData._id}
                          draggable
                          onDragStart={() => handleDragStart(inx)}
                          onDragOver={() => handleDragOver(inx)}
                          onDragEnd={handleDragEnd}
                        >
                          <Td fontSize={"14px"}>{inx + 1 + "."}</Td>
                          <Td
                            style={{
                              whiteSpace: "pre-wrap",
                              wordWrap: "break-word",
                            }}
                            fontSize={"14px"}
                          >
                            {academyData?.academy?.name}
                          </Td>
                          <Td
                            style={{
                              whiteSpace: "pre-wrap",
                              wordWrap: "break-word",
                            }}
                            fontSize={"14px"}
                          >
                            {academyData?.academy?.email}
                          </Td>
                          <Td fontSize={"14px"}>
                            {academyData?.academy?.sportsCategories?.length > 0 ? (
                              <UnorderedList>
                                {(academyData?.academy?.sportsCategories || []).map(
                                  (cat, indx) => (
                                    <ListItem key={indx}>{cat}</ListItem>
                                  )
                                )}
                              </UnorderedList>
                            ) : (
                              "n/a"
                            )}
                          </Td>
                          <Td fontSize={"14px"}>
                            {" "}
                            <Badge
                              colorScheme={
                                academyData?.academy?.status === "active"
                                  ? "green"
                                  : "red"
                              }
                            >
                              {academyData?.academy?.status?.toUpperCase()}
                            </Badge>
                          </Td>
                          <Td fontSize={"14px"}>
                            {" "}
                            <Badge
                              colorScheme={
                                academyData?.academy?.authStatus === "authorized"
                                  ? "green"
                                  : "red"
                              }
                            >
                              {academyData?.academy?.authStatus?.toUpperCase()}
                            </Badge>
                          </Td>
                          <Td fontSize={"14px"}>
                            {academyData?.academy?.gstNumber
                              ? Math.trunc(academyData?.academy?.gstNumber) +
                                " Years"
                              : "n/a"}
                          </Td>
                          <Td fontSize={"14px"}>
                            {!adjustBtnEdit ? (
                              userData?.accessScopes?.cms?.includes(
                                "delete"
                              ) && (
                                <Flex
                                  justifyContent={"center"}
                                  alignItems={"center"}
                                  cursor={"pointer"}
                                  onClick={() => {
                                    setIsUpdated(true);
                                    setCmsAcademyData((prevState) => ({
                                      ...prevState,
                                      result: prevState?.result.filter(
                                        (obj) =>
                                          obj?.academy?._id !==
                                          academyData?.academy?._id
                                      ),
                                    }));
                                  }}
                                >
                                  <Tooltip label="Delete Academy">
                                    <Text>
                                      <MdDelete fontSize={"24px"} />
                                    </Text>
                                  </Tooltip>
                                </Flex>
                              )
                            ) : (
                              <Flex
                                justifyContent={"center"}
                                alignItems={"center"}
                              >
                                <Text as={"span"} ml={3}>
                                  <MdDragHandle
                                    style={{ cursor: "grab" }}
                                    fontSize={"24px"}
                                  />
                                </Text>
                              </Flex>
                            )}
                          </Td>
                        </Tr>
                      ) : (
                        <Tr key={academyData._id}>
                          <Td fontSize={"14px"}>{inx + 1 + "."}</Td>
                          <Td
                            style={{
                              whiteSpace: "pre-wrap",
                              wordWrap: "break-word",
                            }}
                            fontSize={"14px"}
                          >
                            {academyData?.academy?.name}
                            
                          </Td>
                          <Td
                            style={{
                              whiteSpace: "pre-wrap",
                              wordWrap: "break-word",
                            }}
                            fontSize={"14px"}
                          >
                            {academyData?.academy?.email}
                            
                          </Td>
                          <Td fontSize={"14px"}>
                            {academyData?.academy?.sportsCategories?.length > 0 ? (
                              <UnorderedList>
                                {(academyData?.academy?.sportsCategories || []).map(
                                  (cat, indx) => (
                                    <ListItem key={indx}>{cat}</ListItem>
                                  )
                                )}
                              </UnorderedList>
                            ) : (
                              "n/a"
                            )}
                          </Td>
                          <Td fontSize={"14px"}>
                            {" "}
                            <Badge
                              colorScheme={
                                academyData?.academy?.status === "active"
                                  ? "green"
                                  : "red"
                              }
                            >
                              {academyData?.academy?.status?.toUpperCase()}
                            </Badge>
                          </Td>
                          <Td fontSize={"14px"}>
                            {" "}
                            <Badge
                              colorScheme={
                                academyData?.academy?.authStatus === "authorized"
                                  ? "green"
                                  : "red"
                              }
                            >
                              {academyData?.academy?.authStatus?.toUpperCase()}
                            </Badge>
                          </Td>
                          <Td fontSize={"14px"}>
                            {academyData?.academy?.gstNumber}
                          </Td>
                          <Td fontSize={"14px"}>
                            {!adjustBtnEdit ? (
                              userData?.accessScopes?.cms?.includes(
                                "delete"
                              ) && (
                                <Flex
                                  justifyContent={"center"}
                                  alignItems={"center"}
                                  cursor={"pointer"}
                                  onClick={() => {
                                    setIsUpdated(true);
                                    setIsDeleted(true);
                                    setCmsAcademyData((prevState) => ({
                                      ...prevState,
                                      result: prevState?.result.filter(
                                        (obj) =>
                                          obj?.academy?._id !==
                                          academyData?.academy?._id
                                      ),
                                    }));
                                  }}
                                >
                                  <Tooltip label="Delete Academy">
                                    <Text>
                                      <MdDelete fontSize={"24px"} />
                                    </Text>
                                  </Tooltip>
                                </Flex>
                              )
                            ) : (
                              <Flex
                                justifyContent={"center"}
                                alignItems={"center"}
                              >
                                <Text as={"span"} ml={3}>
                                  <MdDragHandle
                                    style={{ cursor: "grab" }}
                                    fontSize={"24px"}
                                  />
                                </Text>
                              </Flex>
                            )}
                          </Td>
                        </Tr>
                      )}
                    </>
                  );
                })
              )}
            </Tbody>
          </Table>
        </TableContainer>
      )}

      {/* Modal for course search */}
      <Modal isOpen={isOpen} onClose={onClose} size="6xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            <Flex justifyContent={"space-between"} alignItems={"center"}>
              <Text mb={0}>Search Academy</Text>
              <Flex>
                {!(!cmsAcademyData?.isLoading && cmsAcademyData?.error) &&
                  !adjustBtnEdit &&
                  userData?.accessScopes?.cms?.includes("write") && (
                    <Flex
                      justifyContent={"flex-end"}
                      alignItems={"center"}
                      mr={7}
                    >
                      <Button
                        variant={"outline"}
                        colorScheme="red"
                        size={"sm"}
                        py={3}
                        px={4}
                        mr={2}
                        isDisabled={!isUpdated}
                        onClick={() => {
                          getCmsAcademyData();
                          setIsUpdated(false);
                          onClose();
                        }}
                      >
                        Discard
                      </Button>
                      <Button
                        variant={"outline"}
                        colorScheme="green"
                        size={"sm"}
                        py={3}
                        px={4}
                        isDisabled={!isUpdated}
                        onClick={onOpen1}
                        isLoading={saveChangesBtnLoading}
                      >
                        Save Changes
                      </Button>
                    </Flex>
                  )}
                <ModalCloseButton
                  onClick={() => {
                    getCmsAcademyData();
                    setIsUpdated(false);
                  }}
                />
              </Flex>
            </Flex>
          </ModalHeader>

          <ModalBody>
            <Card mt={3} variant="outline" height={"95%"}>
              <CardBody>
                <Box>
                  <Stack direction="row" spacing={4} align="center">
                    <Input
                      type="text"
                      placeholder="Search Academy"
                      onChange={(e) => setSearchQuery(e.target.value)}
                      onKeyDown={(e) =>
                        e.key === "Enter" && getAcademyData(searchQuery)
                      }
                    />
                    <Button
                      variant="solid"
                      colorScheme="telegram"
                      onClick={() => getAcademyData(searchQuery)}
                      isDisabled={!(searchQuery.length > 0)}
                    >
                      Search
                    </Button>
                  </Stack>
                  {/* Searched Course List */}
                   {!academyData?.isLoading && academyData?.error ? (
                      <Flex
                        justifyContent={"center"}
                        alignItems={"center"}
                        w={"full"}
                        my={10}
                      >
                        <Text color={"red.500"}>
                          Something went wrong please try again later...
                        </Text>
                      </Flex>
                    ) : (academyData?.result || []).length > 0 ? (
                      <TableContainer
                        mt={6}
                        height={`${window.innerHeight - 300}px`}
                        overflowY={"scroll"}
                      >
                        <Table variant="simple">
                          <Thead
                            bgColor={"#c1eaee"}
                            position={"sticky"}
                            top={"0px"}
                            zIndex={"99"}
                          >
                            <Tr bgColor={"#E2DFDF"}>
                              <Th>S.No</Th>
                              <Th>Academy Name</Th>
                              <Th>Email</Th>
                              <Th>Categories</Th>
                              <Th>Status</Th>
                              <Th>Auth Status</Th>
                              <Th>GST Number</Th>
                              <Th textAlign={"center"}>Action</Th>
                            </Tr>
                          </Thead>
                          <Tbody>
                            {(academyData?.result || []).map((academyData, inx) => {
                              return (
                                <Tr key={academyData?._id}>
                                  <Td>{inx + 1 + "."}</Td>
                                  <Td
                                    style={{
                                      whiteSpace: "pre-wrap",
                                      wordWrap: "break-word",
                                    }}
                                  >
                                    {academyData?.name}
                                  </Td>
                                  <Td>
                                    {academyData?.email}
                                  </Td>
                                  <Td>
                                    {(academyData?.sportsCategories || []).length > 0 ? (
                                      <UnorderedList>
                                        {(academyData?.sportsCategories || []).map(
                                          (cat, indx) => (
                                            <ListItem key={indx}>
                                              {cat}
                                            </ListItem>
                                          )
                                        )}
                                      </UnorderedList>
                                    ) : (
                                      "n/a"
                                    )}
                                  </Td>
                                  <Td>
                                    <Badge
                                      colorScheme={
                                        academyData?.status === "active"
                                          ? "green"
                                          : "red"
                                      }
                                    >
                                      {academyData?.status?.toUpperCase()}
                                    </Badge>
                                  </Td>
                                  <Td>
                                    <Badge
                                      colorScheme={
                                        academyData?.authStatus === "authorized"
                                          ? "green"
                                          : "red"
                                      }
                                    >
                                      {academyData?.authStatus?.toUpperCase()}
                                    </Badge>
                                  </Td>
                                  <Td>
                                    {academyData?.gstNumber}
                                  </Td>
                                  <Td>
                                    {cmsAcademyData?.result.every(
                                      (z) => z?.academy?._id !== academyData?._id
                                    ) ? (
                                      <Button
                                        colorScheme="telegram"
                                        isDisabled={
                                          academyData?.status !== "active" ||
                                          academyData?.authStatus !== "authorized"
                                        }
                                        size={"sm"}
                                        onClick={() => {
                                          setIsUpdated(true);
                                          setCmsAcademyData((prevState) => ({
                                            ...prevState,
                                            result: [
                                              ...prevState.result,
                                              { academy: academyData },
                                            ],
                                          }));
                                        }}
                                      >
                                        Add
                                      </Button>
                                    ) : (
                                      <Button
                                        colorScheme="red"
                                        size={"sm"}
                                        onClick={() => {
                                          setIsUpdated(true);
                                          setCmsAcademyData((prevState) => ({
                                            ...prevState,
                                            result: prevState.result.filter(
                                              (obj) =>
                                                obj?.academy?._id !==
                                                academyData?._id
                                            ),
                                          }));
                                        }}
                                      >
                                        Remove
                                      </Button>
                                    )}
                                  </Td>
                                </Tr>
                              );
                            })}
                          </Tbody>
                        </Table>
                      </TableContainer>
                    ) : academyData?.isLoading && !academyData?.error ? (
                      <Flex
                        justifyContent={"center"}
                        alignItems={"center"}
                        mt={6}
                      >
                        <Spinner size={"lg"} />
                      </Flex>
                    ) : (
                      <Flex
                        justifyContent={"center"}
                        alignItems={"center"}
                        mt={6}
                      >
                        <Text
                          fontSize={"18px"}
                          fontWeight={"semibold"}
                          color={"red.300"}
                        >
                          Result not found
                        </Text>
                      </Flex>
                    )}
                </Box>
              </CardBody>
            </Card>
          </ModalBody>
        </ModalContent>
      </Modal>
      {/* Save Changes Alert */}
      <AlertDialog
        motionPreset="slideInBottom"
        onClose={onClose1}
        isOpen={isOpen1}
        isCentered
      >
        <AlertDialogOverlay />

        <AlertDialogContent>
          <AlertDialogHeader>Save Changes</AlertDialogHeader>
          <AlertDialogCloseButton />
          <AlertDialogBody>
            Are you sure you want to make these changes.
          </AlertDialogBody>
          <AlertDialogFooter>
            <Button onClick={onClose1}>No</Button>
            <Button
              colorScheme="telegram"
              ml={3}
              onClick={() => updateAndSave()}
            >
              Yes
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Layout>
  );
};

export default TopAcademies;
