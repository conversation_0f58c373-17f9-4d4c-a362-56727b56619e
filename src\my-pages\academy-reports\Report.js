import React, { useEffect, useState } from "react";
import Layout from "../../layout/default";
import "react-datepicker/dist/react-datepicker.css";

import { IoIosClose, IoMdSearch } from "react-icons/io";
import { MdDownload } from "react-icons/md";
import {
  Box,
  BreadcrumbLink,
  Input,
  Breadcrumb,
  BreadcrumbItem,
  Card,
  CardBody,
  Button,
  Stack,
  Text,
  Flex,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  TableContainer,
  Table,
  Tbody,
  Tr,
  Td,
  Thead,
  Th,
  useToast,
  Spinner,
  Tooltip,
  Badge,
  Select,
  InputGroup,
  InputRightAddon,
  Checkbox,
} from "@chakra-ui/react";
import axios from "axios";
import { useSelector } from "react-redux";
import ReactPaginate from "react-paginate";
import { Link, useNavigate } from "react-router-dom";
import moment from "moment-timezone";
import InvoicePrint from "./InvoicePrint";
const AcademyReport = () => {
  const [contactData, setContactData] = useState({
    result: [],
    isLoading: false,
    error: false,
    notFound: false,
  });
  
  const [academySearchDetails, setAcademySearchDetails] = useState({
    result: [],
    isLoading: false,
    error: false,
  });

  // Add error boundary state
  const [hasError, setHasError] = useState(false);

  const today = moment().tz("Asia/Kolkata").format("YYYY-MM-DD");
  const [searchQueryAcademy, setSearchQueryAcademy] = useState("");
  const [searchCourseName, setSearchCourseName] = useState("");
  const [selectedClassType, setSelectedClassType] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");
  const [startDate, setStartDate] = useState(today);
  const [endDate, setEndDate] = useState(today);
  const [minEndDate, setMinEndDate] = useState(today);
  const [maxEndDate, setMaxEndDate] = useState(today);
  const [isSearched, setIsSearched] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [showSearch, setShowSearch] = useState(false);
  const [selectedAcademies, setSelectedAcademies] = useState([]);
  
  // Modal states
  const [isOpen2, setIsOpen2] = useState(false);
  const [isOpen3, setIsOpen3] = useState(false);
  const [isOpen4, setIsOpen4] = useState(false);
  const [isOpenWarning, setIsOpenWarning] = useState(false);
  
  const [selectedContact, setSelectedContact] = useState(null);
  const [newStatus, setNewStatus] = useState("");
  const [singleReport, setSingleReport] = useState({});
  const [invoiceModal, setInvoiceModal] = useState(false);
  const [singleReportIndex, setSingleReportIndex] = useState(0);

  const toast = useToast();
  const navigate = useNavigate();
  const userData = useSelector((state) => state.user);

  // Modal handlers
  const onClose2 = () => setIsOpen2(false);
  const onClose3 = () => setIsOpen3(false);
  const onOpen3 = () => setIsOpen3(true);
  const onClose4 = () => {
    setIsOpen4(false);
    if (selectedAcademies.length > 0) {
      getCourseData(searchCourseName, selectedClassType, selectedStatus);
    }
  };
  const onOpen4 = () => setIsOpen4(true);
  const onCloseWarning = () => setIsOpenWarning(false);
  
  // Safe token extraction
  const token = React.useMemo(() => {
    try {
      const adminToken = sessionStorage.getItem("admintoken");
      return adminToken ? adminToken.split(" ")[1] : null;
    } catch (error) {
      console.error("Error getting token:", error);
      return null;
    }
  }, []);

  // Error boundary effect - MUST be called before any early returns
  React.useEffect(() => {
    const handleError = (error) => {
      console.error("Component error:", error);
      setHasError(true);
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  // All useEffect hooks MUST be called before any early returns
  useEffect(() => {
    const start = moment(startDate).tz("Asia/Kolkata").startOf("day");
    const minEnd = start.clone();
    const maxEnd = moment.min(start.clone().add(7, "days"), moment(today));
    setMinEndDate(minEnd.format("YYYY-MM-DD"));
    setMaxEndDate(maxEnd.format("YYYY-MM-DD"));
    if (moment(endDate).isBefore(minEnd) || moment(endDate).isAfter(maxEnd)) {
      setEndDate(minEnd.format("YYYY-MM-DD"));
    }
  }, [startDate, today, endDate]);

  useEffect(() => {
    if (token) {
      getCourseData(searchCourseName, selectedClassType, selectedStatus);
    }
  }, [
    currentPage,
    searchCourseName,
    selectedClassType,
    selectedStatus,
    startDate,
    endDate,
    selectedAcademies,
    token
  ]);

  useEffect(() => {
    if (token) {
      getAcademy(searchQueryAcademy, currentPage);
    }
  }, [currentPage, token]);

  useEffect(() => {
    if (token) {
      getAcademy("", 1);
    }
  }, [token]);

  // If there's an error, show error UI
  if (hasError) {
    return (
      <Layout title="Academy Reports" content="container">
        <Flex justifyContent="center" alignItems="center" h="400px">
          <Text color="red.500" fontSize="lg">
            Something went wrong. Please refresh the page.
          </Text>
        </Flex>
      </Layout>
    );
  }

  // If no token, redirect to login
  if (!token) {
    return (
      <Layout title="Academy Reports" content="container">
        <Flex justifyContent="center" alignItems="center" h="400px">
          <Text color="red.500" fontSize="lg">
            Please login to access this page.
          </Text>
        </Flex>
      </Layout>
    );
  }

  const checkDateRange = (start, end) => {
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 10;
  };

  const getCourseData = (searchCourseName, classType, status) => {
    setContactData({
      result: [],
      isLoading: true,
      error: false,
      notFound: false,
    });

    let queryString = `?page=${currentPage}`;
    queryString += `&isAcademy=true`;
    if (startDate) {
      queryString += `&startDate=${startDate}`;
    }
    if (endDate) {
      queryString += `&endDate=${endDate}`;
    }
    if (searchCourseName) {
      queryString += `&courseName=${searchCourseName}`;
    }
    if (status) {
      queryString += `&paymentStatus=${status}`;
    }
    if (classType) {
      queryString += `&userType=${classType}`;
    }
    if (selectedAcademies.length > 0) {
      selectedAcademies.forEach((academyId) => {
        queryString += `&academyId=${academyId}`;
      });
    }
    let config = {
      method: "post",
      url: `${process.env.REACT_APP_BASE_URL}/api/booking/reports${queryString}`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        setContactData({
          result: response.data.report,
          isLoading: false,
          error: false,
          notFound: response.data.report.length === 0,
        });
        setTotalPages(Math.ceil(response.data.totalResults / 25));
      })
      .catch((error) => {
        console.log(error);
        setContactData({
          result: [],
          isLoading: false,
          error: true,
          notFound: false,
        });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };
  console.log("--contactData", contactData);
  const handleDownload = (searchCourseName, classType, status) => {
    let queryString = `?page=${currentPage}`;
       queryString += `&isAcademy=true`;
    if (startDate) {
      queryString += `&startDate=${startDate}`;
    }
    if (endDate) {
      queryString += `&endDate=${endDate}`;
    }
    if (searchCourseName) {
      queryString += `&courseName=${searchCourseName}`;
    }
    if (status) {
      queryString += `&paymentStatus=${status}`;
    }
    if (classType) {
      queryString += `&userType=${classType}`;
    }
    if (selectedAcademies.length > 0) {
      selectedAcademies.forEach((academyId) => {
        queryString += `&academyId=${academyId}`;
      });
    }
    let config = {
      method: "post",
      url: `${process.env.REACT_APP_BASE_URL}/api/booking/downloadReports${queryString}`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", "report.csv");
        document.body.appendChild(link);
        link.click();
      })
      .catch((error) => {
        console.log(error);
        setContactData({
          result: [],
          isLoading: false,
          error: true,
          notFound: false,
        });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };
  const getAcademy = async (name, page) => {
    try {
      setAcademySearchDetails(prev => ({ ...prev, isLoading: true, error: false }));
      
      let url = "";
      if (name) {
        url = `${process.env.REACT_APP_BASE_URL}/api/academy?name=${encodeURIComponent(name)}&status=active&authStatus=authorized`;
      } else {
        url = `${process.env.REACT_APP_BASE_URL}/api/academy?page=${page}&status=active&authStatus=authorized`;
      }

      const config = {
        method: "get",
        maxBodyLength: Infinity,
        url: url,
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };

      const response = await axios.request(config);
      
      setAcademySearchDetails({
        result: Array.isArray(response.data.data.academies) ? response.data.data.academies : [],
        isLoading: false,
        error: false,
      });
      
      if (response.data.data.academies && response.data.data.academies.length === 0) {
        setIsSearched(true);
      }
    } catch (error) {
      console.error("Error fetching academy data:", error);
      setAcademySearchDetails({ 
        result: [], 
        isLoading: false, 
        error: true 
      });
      
      if (error.response?.status === 403) {
        toast({
          title: "You don't have access to perform this action",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else if (error.response?.status === 401) {
        toast({
          title: "Session expired, please login again",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
        navigate('/login');
      } else {
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
    }
  };

  const handlePageChange = ({ selected }) => {
    setCurrentPage(selected + 1);
  };

  const handleStatusChange = (contact, newStatus) => {
    setSelectedContact(contact);
    setNewStatus(newStatus);
    onOpen3();
  };

  const confirmStatusChange = () => {
    if (!selectedContact || !newStatus) return;
    let config = {
      method: "post",
      url: `${process.env.REACT_APP_BASE_URL}/api/booking/markPaymentStatusPaid?classId=${selectedContact.classId}`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then(() => {
        toast({
          title: "Status updated successfully",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
        getCourseData(searchCourseName, selectedClassType, selectedStatus);
      })
      .catch((error) => {
        console.log(error);
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      })
      .finally(() => {
        onClose3();
      });
  };
  const handleAcademySelect = (e, academyId) => {
    if (e.target.checked) {
      setSelectedAcademies([...selectedAcademies, academyId]);
    } else {
      setSelectedAcademies(selectedAcademies.filter((id) => id !== academyId));
    }
  };

  return (
    <Layout title="Contact" content="container">
      <Flex
        w={"100%"}
        justifyContent={"space-between"}
        alignItems={"center"}
        mb={6}
      >
        {showSearch ? (
          <Box flexBasis={"58%"}>
            <InputGroup size="md">
              <Input
                pr="4.5rem"
                type="text"
                placeholder="Search"
                borderColor={"gray.300"}
                onChange={(e) => {
                  if (e.target.value.length >= 3) {
                    setTimeout(() => {
                      setSearchCourseName(e.target.value);
                    }, 500);
                  }
                  if (e.target.value.length === 0) {
                    setSearchCourseName("");
                  }
                }}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    if (e.target.value.length >= 3) {
                      setSearchCourseName(e.target.value);
                    }
                  }
                }}
              />
              <InputRightAddon
                bgColor={"gray.300"}
                border={"1px"}
                borderColor={"gray.300"}
                onClick={() => {
                  setShowSearch(false);
                  setSearchCourseName("");
                }}
                cursor={"pointer"}
              >
                <IoIosClose fontSize={"24px"} />
              </InputRightAddon>
            </InputGroup>
          </Box>
        ) : (
          <Flex
            flexBasis={"59%"}
            justifyContent={"space-between"}
            alignItems={"center"}
          >
            <Breadcrumb fontWeight="medium" fontSize="sm">
              <BreadcrumbItem>
                <Link to={"/"}>Dashboard</Link>
              </BreadcrumbItem>

              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">Reports</BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>

            <Box
              flexBasis={"80%"}
              display="flex"
              flexDirection="row"
              zIndex={20}
              justifyContent="space-evenly"
              textAlign={"center"}
              alignItems={"center"}
            >
              Choose Date
              <input
                type="date"
                name="start_date"
                value={startDate}
                id="start_date"
                max={today}
                onChange={(e) => {
                  setCurrentPage(1);
                  setStartDate(e.target.value);
                }}
              />
              <input
                type="date"
                name="end_date"
                value={endDate}
                id="end_date"
                min={minEndDate}
                max={maxEndDate}
                onChange={(e) => {
                  setCurrentPage(1);
                  setEndDate(e.target.value);
                }}
              />
            </Box>
          </Flex>
        )}
        <Flex flexBasis={"40%"} justifyContent={"space-evenly"}>
          <Box flexBasis={"31%"}>
            <Select
              placeholder="Status"
              borderColor={"gray.300"}
              cursor={"pointer"}
              bgColor={selectedStatus && "gray.300"}
              onChange={(e) => {
                setSelectedStatus(e.target.value);
                setCurrentPage(1);
              }}
            >
              <option value="">All</option>
              <option value="paid">Paid</option>
              <option value="unpaid">Unpaid</option>
            </Select>
          </Box>

          <Box flexBasis={"31%"}>
            <Button
              variant={"outline"}
              colorScheme="teal"
              size={"sm"}
              px={4}
              isDisabled={!userData?.accessScopes?.course?.includes("write")}
              onClick={() => {
                onOpen4();
                setIsSearched(false);
                setSearchQueryAcademy("");
                getAcademy("", 1);
              }}
              width={"100%"}
              height={"100%"}
            >
              Search By Academy Name
            </Button>
          </Box>
        </Flex>
        <Button onClick={handleDownload}>Download</Button>
      </Flex>

      {invoiceModal && (
        <InvoicePrint
          invoiceModal={invoiceModal}
          setInvoiceModal={setInvoiceModal}
          singleReport={singleReport}
          singleReportIndex={singleReportIndex}
        />
      )}

      {/* Added/Selected Course List   total amount Received , course fee normal , course gst(coach), UMN fee(platform fee), UMN gst(), refund, */}
      {!contactData?.isLoading && contactData?.error ? (
        <Flex
          justifyContent={"center"}
          alignItems={"center"}
          w={"full"}
          my={10}
        >
          <Text color={"red.500"}>
            Something went wrong please try again later...
          </Text>
        </Flex>
      ) : (
        <TableContainer
          mt={6}
          height={`${window.innerHeight - 235}px`}
          overflowY={"scroll"}
        >
          <Table variant="simple">
            <Thead
              bgColor={"#c1eaee"}
              position={"sticky"}
              top={"0px"}
              zIndex={"2"}
            >
              <Tr bgColor={"#E2DFDF"}>
                <Th>Booking Id</Th>
                <Th>Date</Th>
                <Th>Course Name</Th>
                <Th>Coach Name</Th>
                <Th>Total Amount Received</Th>
                <Th>Course fees </Th>
                <Th>Course GST</Th>
                <Th>UMN fee</Th>
                <Th>UMN GST</Th>
                <Th>Refunded Amount (is any)</Th>
                <Th>TDS</Th>

                <Th>Amount due to coach</Th>
                <Th>Coach attendance</Th>
                <Th>Player attendance</Th>
                <Th>Invoice</Th>
                <Th>Status</Th>
              </Tr>
            </Thead>

            <Tbody>
              {contactData?.isLoading && !contactData?.error ? (
                <Tr>
                  <Td></Td>
                  <Td> </Td>
                  <Td
                    display={"flex"}
                    justifyContent={"flex-end"}
                    alignItems={"center"}
                  >
                    <Spinner />
                  </Td>
                  <Td></Td>
                  <Td></Td>
                </Tr>
              ) : // {singleReport?.bookingId}_{singleReportIndex +1}
              !contactData?.notFound ? (
                contactData?.result.map((report, inx) => (
                  <Tr key={inx}>
                    <Td>{report?.bookingId}</Td>
                    <Td>{report?.date?.split("T")[0]}</Td>
                    <Td>
                      {report?.courseName.split(" ").slice(0, 4).join(" ")}
                    </Td>
                    <Td>{report?.coachName}</Td>
                    <Td>
                      {(() => {
                        const classFees = report?.classFees || 0;

                        const gst = report.hasGst ? classFees * (18 / 118) : 0;
                        console.log("---gst", gst);

                        const baseAmount = classFees / 1.18;
                        console.log("---baseAmount", baseAmount);

                        const platformFee = baseAmount * 0.12;
                        console.log("---platformFee", platformFee.toFixed(2));

                        const platformFeeGst = platformFee * 0.18;
                        console.log(
                          "---platformFeeGst",
                          platformFeeGst.toFixed(2)
                        );

                        const refund = report?.refundAmount || 0;

                        const total = gst + baseAmount + platformFee + platformFeeGst;
                        console.log("---total", total);

                        const tenPercentOfTotal = total * 0.1;
                        console.log("---tenPercentOfTotal", tenPercentOfTotal);

                        const result = total - tenPercentOfTotal;
                        console.log("---result", result);

                        return total.toFixed(2);
                      })()}
                    </Td>
                    <Td>{((report?.classFees ?? 0) / 1.18).toFixed(2)}</Td>

                    <Td>
                      {report.hasGst
                        ? report?.classFees * (18 / (100 + 18))
                        : 0.0}
                    </Td>
                    <Td>
                      {(report?.classFees
                        ? (report.classFees / 1.18) * 0.12
                        : 0
                      ).toFixed(2)}
                    </Td>

                    <Td>
                      {(report?.classFees
                        ? (report.classFees / 1.18) * 0.12 * 0.18
                        : 0
                      ).toFixed(2)}
                    </Td>

                    {/* <Td>
                      {report?.hasGst
                        ? (
                            parseFloat(report?.classFees?.toFixed(2)) * 0.18 ||
                            0
                          ).toFixed(2)
                        : "N/A"}
                    </Td> */}
                    <Td> {report?.refundAmount?.toFixed(2)}</Td>

                    <Td>{report?.tds?.toFixed(2)}</Td>

                    <Td>{report?.amountReceived?.toFixed(2)}</Td>
                    <Td
                      style={{
                        whiteSpace: "pre-wrap",
                        wordWrap: "break-word",
                      }}
                      px={4}
                      fontSize={"14px"}
                    >
                      <Badge
                        colorScheme={
                          report?.coachAttendance === "present"
                            ? "green"
                            : "red"
                        }
                      >
                        {report?.coachAttendance}
                      </Badge>
                    </Td>
                    <Td
                      style={{
                        whiteSpace: "pre-wrap",
                        wordWrap: "break-word",
                      }}
                      px={4}
                      fontSize={"14px"}
                    >
                      <Badge
                        colorScheme={
                          report?.playerAttendance === "present"
                            ? "green"
                            : "red"
                        }
                      >
                        {report?.playerAttendance}
                      </Badge>
                    </Td>
                    <Td>
                      <Button
                        leftIcon={<MdDownload />}
                        colorScheme="blue"
                        variant="link"
                        onClick={() => {
                          setSingleReport(report);
                          setInvoiceModal(true);
                          setSingleReportIndex(inx);
                        }}
                      >
                        Invoice
                      </Button>
                    </Td>

                    <Td>
                      <Select
                        size="lg"
                        width="100px"
                        defaultValue={report?.paymentStatus}
                        onChange={(e) =>
                          handleStatusChange(report, e.target.value)
                        }
                        bgColor={
                          report?.paymentStatus === "paid"
                            ? "green.100"
                            : "red.100"
                        }
                        isDisabled={report?.paymentStatus === "paid"}
                      >
                        <option value="paid" style={{ color: "green" }}>
                          Paid
                        </option>
                        <option value="unpaid" style={{ color: "red" }}>
                          Unpaid
                        </option>
                      </Select>
                    </Td>
                  </Tr>
                ))
              ) : (
                <Tr>
                  <Td></Td>
                  <Td> </Td>
                  <Td>No Result</Td>
                  <Td></Td>
                  <Td></Td>
                </Tr>
              )}
            </Tbody>
          </Table>
        </TableContainer>
      )}
      {contactData.result.length > 0 && (
        <Flex justifyContent="flex-end" mt={4}>
          <ReactPaginate
            previousLabel={"Previous"}
            nextLabel={"Next"}
            breakLabel={"..."}
            breakClassName={"break-me"}
            pageCount={totalPages}
            marginPagesDisplayed={2}
            pageRangeDisplayed={5}
            onPageChange={handlePageChange}
            containerClassName={"pagination"}
            activeClassName={"active"}
            forcePage={currentPage - 1}
          />
        </Flex>
      )}
      <Modal isOpen={isOpenWarning} onClose={onCloseWarning}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Warning</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Text>The selected date range should not exceed 10 days.</Text>
          </ModalBody>
          <ModalFooter>
            <Button colorScheme="blue" onClick={onCloseWarning}>
              OK
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
      <Modal isOpen={isOpen3} onClose={onClose3}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Update Status</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            Is this query is resolved, if YES then make it to inactive.
          </ModalBody>
          <ModalFooter>
            <Button colorScheme="red" mr={3} onClick={onClose3}>
              Cancel
            </Button>
            <Button colorScheme="green" onClick={confirmStatusChange}>
              Save Changes
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
      <Modal isOpen={isOpen4} onClose={onClose4} size="5xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Select Academy</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Card mt={3} variant="outline" height={"95%"}>
              <CardBody>
                <Box>
                  <Stack direction="row" spacing={4} align="center">
                    <Input
                      type="text"
                      placeholder="Search Academy"
                      value={searchQueryAcademy}
                      onChange={(e) => {
                        setSearchQueryAcademy(e.target.value);
                        setIsSearched(false);
                      }}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          getAcademy(searchQueryAcademy, 1);
                          setIsSearched(false);
                        }
                      }}
                    />
                    <Button
                      variant="solid"
                      colorScheme="telegram"
                      onClick={() => {
                        getAcademy(searchQueryAcademy, 1);
                        setIsSearched(false);
                      }}
                      isDisabled={!(searchQueryAcademy.length > 0)}
                    >
                      Search
                    </Button>
                  </Stack>
                  <Card mt={6}>
                    {!academySearchDetails?.isLoading && academySearchDetails?.error ? (
                      <Flex justifyContent={"center"} alignItems={"center"} w={"full"} my={10}>
                        <Text color={"red.500"}>
                          Something went wrong please try again later...
                        </Text>
                      </Flex>
                    ) : (
                      <TableContainer height={`${window.innerHeight - 300}px`} overflowY={"scroll"}>
                        <Table variant="simple">
                          <Thead bgColor={"#c1eaee"} position={"sticky"} top={"0px"} zIndex={"99"}>
                            <Tr bgColor={"#E2DFDF"}>
                              <Th>S.No</Th>
                              <Th>Name</Th>
                              <Th>Email</Th>
                              <Th>Mobile</Th>
                              <Th>Status</Th>
                              <Th>Auth Status</Th>
                            </Tr>
                          </Thead>
                          <Tbody>
                            {academySearchDetails?.isLoading && !academySearchDetails?.error ? (
                              <Tr>
                                <Td></Td>
                                <Td></Td>
                                <Td></Td>
                                <Td display={"flex"} justifyContent={"center"} alignItems={"center"}>
                                  <Spinner />
                                </Td>
                                <Td></Td>
                                <Td></Td>
                              </Tr>
                            ) : !academySearchDetails?.isLoading && academySearchDetails?.error ? (
                              <Flex justifyContent={"center"} alignItems={"center"} w={"full"} my={10}>
                                <Text color={"red.500"}>
                                  Something went wrong please try again later...
                                </Text>
                              </Flex>
                            ) : !isSearched ? (
                              academySearchDetails?.result?.map((academy, i) => {
                                return (
                                  <Tr key={academy._id}>
                                    <Td>
                                      <Checkbox
                                        isChecked={selectedAcademies.includes(academy._id)}
                                        onChange={(e) => handleAcademySelect(e, academy._id)}
                                      />
                                    </Td>
                                    <Td style={{ whiteSpace: "pre-wrap", wordWrap: "break-word" }} fontSize={"14px"}>
                                      {academy?.name}
                                    </Td>
                                    <Td fontSize={"14px"}>{academy?.email}</Td>
                                    <Td fontSize={"14px"}>{academy?.mobile}</Td>
                                    <Td fontSize={"14px"}>
                                      <Badge colorScheme={academy?.status === "active" ? "green" : "red"}>
                                        {academy?.status}
                                      </Badge>
                                    </Td>
                                    <Td fontSize={"14px"}>
                                      <Badge
                                        colorScheme={
                                          academy?.authStatus === "authorized" 
                                            ? "green" 
                                            : academy?.authStatus === "pending"
                                            ? "yellow"
                                            : "red"
                                        }
                                      >
                                        {academy?.authStatus}
                                      </Badge>
                                    </Td>
                                  </Tr>
                                );
                              })
                            ) : (
                              <Tr>
                                <Td></Td>
                                <Td></Td>
                                <Td></Td>
                                <Td display={"flex"} justifyContent={"flex-end"}>
                                  <Text color={"green.500"} fontWeight={"semibold"}>
                                    No result found
                                  </Text>
                                </Td>
                                <Td></Td>
                                <Td></Td>
                              </Tr>
                            )}
                          </Tbody>
                        </Table>
                      </TableContainer>
                    )}
                  </Card>
                </Box>
              </CardBody>
            </Card>
          </ModalBody>
        </ModalContent>
      </Modal>
    </Layout>
  );
};

export default AcademyReport;
