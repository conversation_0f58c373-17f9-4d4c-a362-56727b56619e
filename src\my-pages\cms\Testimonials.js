import React, { useEffect, useState } from "react";
import Layout from "../../layout/default";

import {
  BreadcrumbLink,
  Breadcrumb,
  BreadcrumbItem,
  Flex,
  Box,
  Button,
  TableContainer,
  Table,
  Thead,
  Th,
  Tbody,
  Tr,
  Td,
  Text,
  Tooltip,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  FormControl,
  FormLabel,
  Textarea,
  Select,
  Input,
  Image,
  useToast,
  FormErrorMessage,
  AlertDialog,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogCloseButton,
  AlertDialogBody,
  AlertDialogFooter,
  Spinner,
} from "@chakra-ui/react";
import { Link } from "react-router-dom";
import axios from "axios";
import { MdDelete, MdDragHandle, MdEdit } from "react-icons/md";
import { useFormik } from "formik";
import * as Yup from "yup";
import { getCookie } from "../../utilities/auth";
import { useSelector } from "react-redux";

const Testimonials = () => {
  const [deleteTestimonialId, setDeleteTestimonialId] = useState("");
  const [updateTestimonialId, setUpdateTestimonialId] = useState("");
  const [testimonialData, setTestimonialData] = useState({
    result: [],
    isLoading: false,
    error: false,
  });
  const [draggedItemIndex, setDraggedItemIndex] = useState(null);
  const [selectedTestimonialData, setSelectedTestimonialData] = useState([]);
  const [adjustBtnEdit, setAdjustBtnEdit] = useState(false);
  const [previousTestimonialData, setPreviousTestimonialData] = useState([]);
  const [posBtnLoading, setPosBtnLoading] = useState(false);
  const [renderMe, setRenderMe] = useState(0);

  const [isOpen1, setIsOpen1] = useState(false);
  const onClose1 = () => setIsOpen1(false);
  const onOpen1 = () => setIsOpen1(true);

  const [isOpen2, setIsOpen2] = useState(false);
  const onClose2 = () => setIsOpen2(false);
  const onOpen2 = () => setIsOpen2(true);

  const [isOpen3, setIsOpen3] = useState(false);
  const onClose3 = () => setIsOpen3(false);
  const onOpen3 = () => setIsOpen3(true);

  const toast = useToast();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const userData = useSelector((state) => state.user);

  const getTestimonials = () => {
    setTestimonialData({ result: [], isLoading: false, error: false });
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/cms/testimonials`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        setTestimonialData({
          result: response.data.sort((a, b) => a.position - b.position),
          isLoading: false,
          error: false,
        });
      })
      .catch((error) => {
        console.log(error);
        setTestimonialData({ result: [], isLoading: false, error: true });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const renderMeComp = () => {
    setRenderMe((prev) => prev + 1);
  };

  const handleDragStart = (index) => {
    setDraggedItemIndex(index);
  };

  const handleDragOver = (index) => {
    if (draggedItemIndex === null) return;
    const newItems = [...testimonialData.result]; // Copy the result array from blockData
    const draggedItem = newItems[draggedItemIndex];
    newItems.splice(draggedItemIndex, 1);
    newItems.splice(index, 0, draggedItem);

    // Adjust positions of items inside newItems array
    newItems.forEach((item, idx) => {
      item.position = idx + 1;
    });

    setTestimonialData({ ...testimonialData, result: newItems }); // Update result array in blockData
    setDraggedItemIndex(index);
  };

  const handleDragEnd = () => {
    setDraggedItemIndex(null);
  };

  const updateBlockPosition = () => {
    setPosBtnLoading(true);
    const value = testimonialData.result.map((x) => {
      return { id: x._id, newPosition: x.position };
    });
    let data = JSON.stringify({
      updates: value,
    });



    let config = {
      method: "patch",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/cms/update/testimonial/position`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
        setPosBtnLoading(false);
        setPreviousTestimonialData([]);
        setAdjustBtnEdit(false);
        toast({
          title: "Bl position updated",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      })
      .catch((error) => {
        console.log(error);
        onClose1();
        setPosBtnLoading(false);
        setPreviousTestimonialData([]);
        setAdjustBtnEdit(false);
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  useEffect(() => {
    getTestimonials();
    setDeleteTestimonialId("");
    setSelectedTestimonialData([]);
  }, [renderMe]);

  return (
    <Layout title="CMS | Testimonials" content="container">
      <Flex justifyContent={"space-between"} alignItems={"center"}>
        <Breadcrumb fontWeight="medium" fontSize="sm">
          <BreadcrumbItem>
            <BreadcrumbLink>
              <Link to={"/"}>Dashboard</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>

          <BreadcrumbItem isCurrentPage>
            <BreadcrumbLink>CMS</BreadcrumbLink>
          </BreadcrumbItem>

          <BreadcrumbItem isCurrentPage>
            <BreadcrumbLink href="#">Testimonials</BreadcrumbLink>
          </BreadcrumbItem>
        </Breadcrumb>
        {!adjustBtnEdit ? (
          userData?.accessScopes?.cms?.includes("write") && (
            <Box>
              <Button
                variant={"outline"}
                colorScheme="telegram"
                size={"sm"}
                py={5}
                px={4}
                mr={3}
                onClick={() => {
                  setAdjustBtnEdit(true);
                  setPreviousTestimonialData(testimonialData.result);
                }}
              >
                Adjust Position
              </Button>
              <Button
                variant={"outline"}
                colorScheme="teal"
                size={"sm"}
                py={5}
                px={4}
                onClick={() => {
                  onOpen1();
                  setSelectedTestimonialData([]);
                }}
              >
                Add Testimonials
              </Button>
            </Box>
          )
        ) : (
          <Flex>
            <Button
              variant={"outline"}
              colorScheme="red"
              size={"sm"}
              py={5}
              px={4}
              mr={4}
              onClick={() => {
                setTestimonialData({
                  result: previousTestimonialData,
                  isLoading: false,
                  error: false,
                });
                setAdjustBtnEdit(false);
              }}
            >
              Discard
            </Button>
            <Button
              variant={"outline"}
              colorScheme="green"
              size={"sm"}
              py={5}
              px={4}
              isLoading={posBtnLoading}
              onClick={updateBlockPosition}
            >
              Save Changes
            </Button>
          </Flex>
        )}
      </Flex>
      {!testimonialData?.isLoading && testimonialData?.error ? (
        <Flex
          justifyContent={"center"}
          alignItems={"center"}
          w={"full"}
          my={10}
        >
          <Text color={"red.500"}>
            Something went wrong please try again later...
          </Text>
        </Flex>
      ) : (
        <TableContainer
          mt={6}
          height={`${window.innerHeight - 222}px`}
          overflowY={"scroll"}
        >
          <Table variant="simple">
            <Thead
              bgColor={"#c1eaee"}
              position={"sticky"}
              top={"0px"}
              zIndex={"99"}
            >
              <Tr bgColor={"#E2DFDF"}>
                <Th>Position</Th>
                <Th>Name</Th>
                <Th>Description</Th>
                <Th>Gender</Th>
                <Th textAlign={"center"}>Action</Th>
              </Tr>
            </Thead>
            <Tbody>
              {testimonialData?.isLoading && !testimonialData?.error ? (
                <Tr>
                  <Td> </Td>
                  <Td></Td>
                  <Td
                    display={"flex"}
                    justifyContent={"center"}
                    alignItems={"center"}
                  >
                    <Spinner />
                  </Td>
                  <Td></Td>
                  <Td></Td>
                </Tr>
              ) : (
                testimonialData.result.map((testimonial, index) => {
                  return (
                    <>
                      {adjustBtnEdit ? (
                        <Tr
                          key={index}
                          draggable
                          onDragStart={() => handleDragStart(index)}
                          onDragOver={() => handleDragOver(index)}
                          onDragEnd={handleDragEnd}
                        >
                          <Td fontSize={"14px"}>{index + 1}.</Td>
                          <Td
                            style={{
                              whiteSpace: "pre-wrap",
                              wordWrap: "break-word",
                            }}
                            fontSize={"14px"}
                          >
                            {testimonial?.name}
                          </Td>
                          <Td fontSize={"14px"}>
                            {testimonial?.description?.substring(0, 20) + "..."}
                          </Td>
                          <Td fontSize={"14px"}>{testimonial?.gender}</Td>
                          <Td fontSize={"14px"}>
                            {!adjustBtnEdit ? (
                              <Flex
                                justifyContent={"space-evenly"}
                                alignItems={"center"}
                              >
                                {userData?.accessScopes?.cms?.includes(
                                  "write"
                                ) && (
                                  <Tooltip label="Edit Testimonials">
                                    <Text
                                      as="span"
                                      cursor={"pointer"}
                                      fontSize={"20px"}
                                      onClick={() => {
                                        onOpen3();
                                        setUpdateTestimonialId(testimonial._id);
                                        setSelectedTestimonialData({
                                          name: testimonial?.name || "",
                                          image: testimonial?.image || "",
                                          description:
                                            testimonial?.description || "",
                                          gender: testimonial?.gender || "",
                                        });
                                      }}
                                    >
                                      <MdEdit />
                                    </Text>
                                  </Tooltip>
                                )}
                                {userData?.accessScopes?.cms?.includes(
                                  "delete"
                                ) && (
                                  <Tooltip label="Delete Testimonials">
                                    <Text
                                      as="span"
                                      cursor={"pointer"}
                                      fontSize={"20px"}
                                      onClick={() => {
                                        setDeleteTestimonialId(testimonial._id);
                                        onOpen2();
                                      }}
                                    >
                                      <MdDelete />
                                    </Text>
                                  </Tooltip>
                                )}
                              </Flex>
                            ) : (
                              <Flex
                                justifyContent={"center"}
                                alignItems={"center"}
                              >
                                <Text as={"span"} ml={3}>
                                  <MdDragHandle
                                    style={{ cursor: "grab" }}
                                    fontSize={"24px"}
                                  />
                                </Text>
                              </Flex>
                            )}
                          </Td>
                        </Tr>
                      ) : (
                        <Tr key={index}>
                          <Td fontSize={"14px"}>{index + 1}.</Td>
                          <Td
                            style={{
                              whiteSpace: "pre-wrap",
                              wordWrap: "break-word",
                            }}
                            fontSize={"14px"}
                          >
                            {testimonial?.name}
                          </Td>
                          <Td fontSize={"14px"}>
                            {testimonial?.description?.substring(0, 20) + "..."}
                          </Td>
                          <Td fontSize={"14px"}>{testimonial?.gender}</Td>
                          <Td fontSize={"14px"}>
                            {!adjustBtnEdit ? (
                              <Flex
                                justifyContent={"space-evenly"}
                                alignItems={"center"}
                              >
                                {userData?.accessScopes?.cms?.includes(
                                  "write"
                                ) && (
                                  <Tooltip label="Edit Testimonials">
                                    <Text
                                      as="span"
                                      cursor={"pointer"}
                                      fontSize={"20px"}
                                      onClick={() => {
                                        onOpen3();
                                        setUpdateTestimonialId(testimonial._id);
                                        setSelectedTestimonialData({
                                          name: testimonial?.name || "",
                                          image: testimonial?.image || "",
                                          description:
                                            testimonial?.description || "",
                                          gender: testimonial?.gender || "",
                                        });
                                      }}
                                    >
                                      <MdEdit />
                                    </Text>
                                  </Tooltip>
                                )}
                                {userData?.accessScopes?.cms?.includes(
                                  "delete"
                                ) && (
                                  <Tooltip label="Delete Testimonials">
                                    <Text
                                      as="span"
                                      cursor={"pointer"}
                                      fontSize={"20px"}
                                      onClick={() => {
                                        setDeleteTestimonialId(testimonial._id);
                                        onOpen2();
                                      }}
                                    >
                                      <MdDelete />
                                    </Text>
                                  </Tooltip>
                                )}
                              </Flex>
                            ) : (
                              <Flex
                                justifyContent={"center"}
                                alignItems={"center"}
                              >
                                <Text as={"span"} ml={3}>
                                  <MdDragHandle
                                    style={{ cursor: "grab" }}
                                    fontSize={"24px"}
                                  />
                                </Text>
                              </Flex>
                            )}
                          </Td>
                        </Tr>
                      )}
                    </>
                  );
                })
              )}
            </Tbody>
          </Table>
        </TableContainer>
      )}
      {/* Testimonials add Data Component */}
      <TestimonialsData
        renderMeComp={renderMeComp}
        onOpen1={onOpen1}
        onOpen2={onOpen2}
        isOpen1={isOpen1}
        isOpen2={isOpen2}
        onClose1={onClose1}
        onClose2={onClose2}
        deleteTestimonialId={deleteTestimonialId}
        selectedTestimonialData={selectedTestimonialData}
      />
      {/* Testimonials update Data Component */}
      <TestimonialsDataUpdate
        renderMeComp={renderMeComp}
        onOpen3={onOpen3}
        isOpen3={isOpen3}
        onClose3={onClose3}
        updateTestimonialId={updateTestimonialId}
        selectedTestimonialData={selectedTestimonialData}
      />
    </Layout>
  );
};

export default Testimonials;

const TestimonialsData = ({
  selectedTestimonialData,
  renderMeComp,
  onClose1,
  onClose2,
  deleteTestimonialId,
  isOpen1,
  isOpen2,
}) => {
  const [deleteBtnLoading, setDeleteBtnLoading] = useState(false);
  const [previewImage, setPreviewImage] = useState("");
  const [isBtnLoading, setIsBtnLoading] = useState(false);
  const toast = useToast();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];

  const validationSchema = Yup.object().shape({
    description: Yup.string()
      .required("Description is required")
      .min(10, "Description must be at least 10 characters"),
    name: Yup.string()
      .required("Full name is required")
      .min(3, "Full name must be at least 3 characters"),
    gender: Yup.string()
      .required("Gender is required")
      .min(3, "Gender must be at least 3 characters"),
  });

  const formik = useFormik({
    initialValues: {
      name: "",
      description: "",
      image: "",
      gender: "",
    },
    validationSchema: validationSchema,
    onSubmit: (values, { resetForm }) => {
      setIsBtnLoading(true);
      let data = JSON.stringify(values);
      let config = {
        method: "post",
        maxBodyLength: Infinity,
        url: `${process.env.REACT_APP_BASE_URL}/api/cms/create/testimonial`,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        data: data,
      };

      axios
        .request(config)
        .then((response) => {
          onClose1();
          setIsBtnLoading(false);
          renderMeComp();
          resetForm();
          setPreviewImage("");
          toast({
            title: "Testimonial added",
            status: "success",
            duration: 3500,
            position: "top",
            isClosable: true,
          });
        })
        .catch((error) => {
          console.log(error);

          if (
            error.response.data.message ===
            "Maximum limit reached, cannot be grater than 15"
          ) {
            setIsBtnLoading(false);
            onClose1();
            setPreviewImage("");
            resetForm();
            removeImage(values.image);
            toast({
              title: `Testimonial cannot be added, maximum limit reached, you can add testimonial upto 15`,
              status: "warning",
              duration: 5000,
              position: "top",
              isClosable: true,
            });
          } else if (error.response.status === 403) {
            toast({
              title: "You don't have an access to perform this action",
              status: "warning",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          } else {
            toast({
              title: "Something went wrong please try again later",
              status: "error",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          }
        });
    },
  });

  // Function to handle image selection
  const handleImageChange = async (e) => {
    const file = e.target.files[0];
    const reader = new FileReader();

    reader.readAsDataURL(file);
    reader.onloadend = () => {
      setPreviewImage(reader.result);
    };

    const formData = new FormData();
    formData.append("image", file);
    try {
      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/api/cms/upload`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const url = response.data.url;
      formik.setFieldValue("image", url);
      setPreviewImage(url);
      toast({
        title: "Image Uploaded",
        status: "success",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
    } catch (error) {
      console.log(error);
      if (error.response.status === 403) {
        toast({
          title: "You don't have an access to perform this action",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
    }
  };

  const removeImage = async (url) => {
    try {
      const formData = new FormData();
      formData.append("url", url);

      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/api/cms/upload`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const resp = response?.data;
      if (resp) {
        if (url) {
          toast({
            title: "Image deleted",
            status: "success",
            duration: 3000,
            position: "top",
            isClosable: true,
          });
          formik.setFieldValue("image", undefined);
          setPreviewImage("");
        } else {
          toast({
            title: "Something went wrong while deleting image.",
            status: "error",
            duration: 3000,
            position: "top",
            isClosable: true,
          });
        }
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 403) {
        toast({
          title: "You don't have an access to perform this action",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
    }
  };

  const deleteTestimonial = () => {
    setDeleteBtnLoading(true);
    let config = {
      method: "delete",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/cms/delete/testimonial/${deleteTestimonialId}`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {

        setDeleteBtnLoading(false);
        renderMeComp();
        onClose2();
        toast({
          title: "Testimonial deleted",
          status: "success",
          duration: 3500,
          position: "top",
          isClosable: true,
        });
      })
      .catch((error) => {
        console.log(error);
        setDeleteBtnLoading(false);
        onClose2();
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  useEffect(() => {
    // Update formik initial values whenever selectedTestimonialData changes
    setPreviewImage(selectedTestimonialData?.image);
    formik.setValues({
      name: selectedTestimonialData?.name || "",
      description: selectedTestimonialData?.description || "",
      image: selectedTestimonialData?.image || "",
      gender: selectedTestimonialData?.gender || "",
    });
  }, [selectedTestimonialData]);

  return (
    <>
      {/* Add Testimonial Modal */}
      <form onSubmit={formik.handleSubmit}>
        <Modal isOpen={isOpen1} onClose={onClose1} size={"xl"}>
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>{"Add Testimonial"}</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              <FormControl
                isInvalid={formik.errors.name && formik.touched.name}
              >
                <FormLabel>Name</FormLabel>
                <Input
                  type="text"
                  placeholder="Enter testimonial full name"
                  id="name"
                  name="name"
                  onChange={formik.handleChange}
                  value={formik.values.name}
                />
                {formik.errors.name && formik.touched.name && (
                  <FormErrorMessage>{formik.errors.name}</FormErrorMessage>
                )}
              </FormControl>
              <FormControl
                my={3}
                isInvalid={
                  formik.errors.description && formik.touched.description
                }
              >
                <FormLabel>Description</FormLabel>
                <Textarea
                  placeholder="Enter testimonial description"
                  id="description"
                  name="description"
                  onChange={formik.handleChange}
                  value={formik.values.description}
                />
                {formik.errors.description && formik.touched.description && (
                  <FormErrorMessage>
                    {formik.errors.description}
                  </FormErrorMessage>
                )}
              </FormControl>
              <FormControl my={3}>
                <FormLabel>Image</FormLabel>
                <Input
                  type="file"
                  id="imageFile"
                  name="imageFile"
                  accept="image/*"
                  onChange={handleImageChange}
                />
                {previewImage && (
                  <Box mt={2}>
                    <Image
                      src={previewImage}
                      alt="Preview"
                      boxSize="150px"
                      objectFit="cover"
                    />
                    <Button
                      mt={2}
                      size="sm"
                      colorScheme="red"
                      onClick={() => removeImage(previewImage)}
                      px={12}
                    >
                      Remove
                    </Button>
                  </Box>
                )}
              </FormControl>
              <FormControl
                my={3}
                isInvalid={formik.errors.gender && formik.touched.gender}
              >
                <FormLabel>Gender</FormLabel>
                <Select
                  placeholder="Select gender"
                  id="gender"
                  name="gender"
                  onChange={formik.handleChange}
                  value={formik.values.gender}
                >
                  <option value="Male">Male</option>
                  <option value="Female">Female</option>
                  <option value="Other">Other</option>
                </Select>
                {formik.errors.gender && formik.touched.gender && (
                  <FormErrorMessage>{formik.errors.gender}</FormErrorMessage>
                )}
              </FormControl>
            </ModalBody>
            <ModalFooter>
              <Button
                colorScheme="red"
                mr={3}
                onClick={() => {
                  onClose1();
                  if (previewImage) {
                    removeImage(previewImage);
                  }
                }}
              >
                Discard
              </Button>
              {!previewImage ? (
                <Tooltip label={"Please select image"} bg={"red.600"} hasArrow>
                  <Button
                    colorScheme="green"
                    size="sm"
                    type="submit"
                    isDisabled={!previewImage}
                  >
                    Save Changes
                  </Button>
                </Tooltip>
              ) : (
                <Button
                  colorScheme="green"
                  size="sm"
                  type="submit"
                  onClick={formik.handleSubmit}
                  isLoading={isBtnLoading}
                >
                  Save Changes
                </Button>
              )}
            </ModalFooter>
          </ModalContent>
        </Modal>
      </form>

      {/* Save Changes Alert */}
      <AlertDialog
        motionPreset="slideInBottom"
        onClose={onClose2}
        isOpen={isOpen2}
        isCentered
      >
        <AlertDialogOverlay />

        <AlertDialogContent>
          <AlertDialogHeader>Delete Testimonial</AlertDialogHeader>
          <AlertDialogCloseButton />
          <AlertDialogBody>
            Are you sure you want to make these changes.
          </AlertDialogBody>
          <AlertDialogFooter>
            <Button
              onClick={() => {
                onClose2();
              }}
            >
              No
            </Button>
            <Button
              colorScheme="telegram"
              ml={3}
              isLoading={deleteBtnLoading}
              onClick={deleteTestimonial}
            >
              Yes
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

const TestimonialsDataUpdate = ({
  selectedTestimonialData,
  renderMeComp,
  onClose3,
  isOpen3,
  updateTestimonialId,
}) => {
  const [previewImage, setPreviewImage] = useState("");
  const [isBtnLoading, setIsBtnLoading] = useState(false);
  const toast = useToast();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];

  const validationSchema = Yup.object().shape({
    description: Yup.string()
      .required("Description is required")
      .min(10, "Description must be at least 10 characters"),
    name: Yup.string()
      .required("Full name is required")
      .min(3, "Full name must be at least 3 characters"),
    gender: Yup.string()
      .required("Gender is required")
      .min(3, "Gender must be at least 3 characters"),
  });

  const formik = useFormik({
    initialValues: {
      name: "",
      description: "",
      image: "",
      gender: "",
    },
    validationSchema: validationSchema,
    onSubmit: (values, { resetForm }) => {
      setIsBtnLoading(true);
      let data = JSON.stringify(values);
      let config = {
        method: "patch",
        maxBodyLength: Infinity,
        url: `${process.env.REACT_APP_BASE_URL}/api/cms/update/testimonial/${updateTestimonialId}`,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        data: data,
      };

      axios
        .request(config)
        .then((response) => {
          onClose3();
          setIsBtnLoading(false);
          renderMeComp();
          resetForm();
          setPreviewImage("");
          toast({
            title: "Testimonial updated",
            status: "success",
            duration: 3500,
            position: "top",
            isClosable: true,
          });
        })
        .catch((error) => {
          console.log(error);
          onClose3();
          setIsBtnLoading(false);
          resetForm();
          setPreviewImage("");
          if (error.response.status === 403) {
            toast({
              title: "You don't have an access to perform this action",
              status: "warning",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          } else {
            toast({
              title: "Something went wrong please try again later",
              status: "error",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          }
        });
    },
  });

  // Function to handle image selection
  const handleImageChange = async (e) => {
    const file = e.target.files[0];
    const reader = new FileReader();

    reader.readAsDataURL(file);
    reader.onloadend = () => {
      setPreviewImage(reader.result);
    };

    const formData = new FormData();
    formData.append("image", file);
    try {
      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/api/cms/upload`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const url = response.data.url;
      formik.setFieldValue("image", url);
      setPreviewImage(url);
      toast({
        title: "Image Uploaded",
        status: "success",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
    } catch (error) {
      console.log(error);
      if (error.response.status === 403) {
        toast({
          title: "You don't have an access to perform this action",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
    }
  };

  const removeImage = async (url) => {
    try {
      const formData = new FormData();
      formData.append("url", url);

      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/api/cms/upload`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const resp = response?.data;
      if (resp) {
        if (url) {
          toast({
            title: "Image deleted",
            status: "success",
            duration: 3000,
            position: "top",
            isClosable: true,
          });
          formik.setFieldValue("image", undefined);
          setPreviewImage("");
        } else {
          toast({
            title: "Something went wrong while deleting image.",
            status: "error",
            duration: 3000,
            position: "top",
            isClosable: true,
          });
        }
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 403) {
        toast({
          title: "You don't have an access to perform this action",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
    }
  };

  useEffect(() => {
    // Update formik initial values whenever selectedTestimonialData changes
    setPreviewImage(selectedTestimonialData?.image);
    formik.setValues({
      name: selectedTestimonialData?.name || "",
      description: selectedTestimonialData?.description || "",
      image: selectedTestimonialData?.image || "",
      gender: selectedTestimonialData?.gender || "",
    });
  }, [selectedTestimonialData]);

  return (
    <>
      {/* Add Testimonial Modal */}
      <form onSubmit={formik.handleSubmit}>
        <Modal isOpen={isOpen3} onClose={onClose3} size={"xl"}>
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>{"Update Testimonial"}</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              <FormControl
                isInvalid={formik.errors.name && formik.touched.name}
              >
                <FormLabel>Name</FormLabel>
                <Input
                  type="text"
                  placeholder="Enter testimonial full name"
                  id="name"
                  name="name"
                  onChange={formik.handleChange}
                  value={formik.values.name}
                />
                {formik.errors.name && formik.touched.name && (
                  <FormErrorMessage>{formik.errors.name}</FormErrorMessage>
                )}
              </FormControl>
              <FormControl
                my={3}
                isInvalid={
                  formik.errors.description && formik.touched.description
                }
              >
                <FormLabel>Description</FormLabel>
                <Textarea
                  placeholder="Enter testimonial description"
                  id="description"
                  name="description"
                  onChange={formik.handleChange}
                  value={formik.values.description}
                />
                {formik.errors.description && formik.touched.description && (
                  <FormErrorMessage>
                    {formik.errors.description}
                  </FormErrorMessage>
                )}
              </FormControl>
              <FormControl my={3}>
                <FormLabel>Image</FormLabel>
                <Input
                  type="file"
                  id="imageFile"
                  name="imageFile"
                  accept="image/*"
                  onChange={handleImageChange}
                />
                {previewImage && (
                  <Box mt={2}>
                    <Image
                      src={previewImage}
                      alt="Preview"
                      boxSize="150px"
                      objectFit="cover"
                    />
                    <Button
                      mt={2}
                      size="sm"
                      colorScheme="red"
                      onClick={() => removeImage(previewImage)}
                      px={12}
                    >
                      Remove
                    </Button>
                  </Box>
                )}
              </FormControl>
              <FormControl
                my={3}
                isInvalid={formik.errors.gender && formik.touched.gender}
              >
                <FormLabel>Gender</FormLabel>
                <Select
                  placeholder="Select gender"
                  id="gender"
                  name="gender"
                  onChange={formik.handleChange}
                  value={formik.values.gender}
                >
                  <option value="Male">Male</option>
                  <option value="Female">Female</option>
                  <option value="Other">Other</option>
                </Select>
                {formik.errors.gender && formik.touched.gender && (
                  <FormErrorMessage>{formik.errors.gender}</FormErrorMessage>
                )}
              </FormControl>
            </ModalBody>
            <ModalFooter>
              <Button
                colorScheme="red"
                mr={3}
                onClick={() => {
                  onClose3();
                }}
              >
                Discard
              </Button>
              {!previewImage ? (
                <Tooltip label={"Please select image"} bg={"red.600"} hasArrow>
                  <Button
                    colorScheme="green"
                    size="sm"
                    type="submit"
                    isDisabled={!previewImage}
                  >
                    Save Changes
                  </Button>
                </Tooltip>
              ) : (
                <Button
                  colorScheme="green"
                  size="sm"
                  type="submit"
                  onClick={formik.handleSubmit}
                  isLoading={isBtnLoading}
                >
                  Save Changes
                </Button>
              )}
            </ModalFooter>
          </ModalContent>
        </Modal>
      </form>
    </>
  );
};
